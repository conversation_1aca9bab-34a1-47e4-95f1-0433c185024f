package com.fytec.controller.external;

import com.fytec.aspect.annotation.ExternalServiceLog;
import com.fytec.constant.ExternalConstants;
import com.fytec.dto.external.XfyunTtsCreateTaskPublicRequestDTO;
import com.fytec.dto.external.XfyunTtsQueryTaskPublicRequestDTO;

import com.fytec.service.external.XfyunTtsService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 讯飞长文本语音合成控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/long-text-tts")
@Tag(name = "讯飞长文本语音合成", description = "讯飞长文本语音合成API接口")
public class XfyunTtsController {

    private final XfyunTtsService xfyunTtsService;

    /**
     * 创建语音合成任务
     *
     * @param request 创建任务请求参数
     * @return 创建任务响应结果
     */
    @PostMapping("/create-task")
    @Operation(summary = "创建语音合成任务", description = "创建长文本语音合成任务，支持多种音色和参数配置")
    @ExternalServiceLog(
            serviceType = ExternalConstants.ServiceType.TTS_PREMIUM_LONG,
            contents = @ExternalServiceLog.Path2Type(
                    contentPath = "$[0].payload.text.text",
                    processType = ExternalServiceLog.FieldProcessType.STRING_TEXT
            ),
            dataMode = ExternalServiceLog.DataMode.REQUEST
    )
    public R<Object> createTask(@Valid @RequestBody XfyunTtsCreateTaskPublicRequestDTO request) {
        log.debug("接收到创建TTS任务请求，文本长度: {}",
                request.getPayload() != null && request.getPayload().getText() != null && request.getPayload().getText().getText() != null ? request.getPayload().getText().getText().length() : 0);
        try {
            Object response = xfyunTtsService.createTask(request);
            return R.ok(response, "创建TTS任务成功");
        } catch (Exception e) {
            log.error("创建TTS任务失败: {}", e.getMessage(), e);
            return R.failed("创建TTS任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询语音合成任务状态
     *
     * @param request 查询任务请求参数
     * @return 查询任务响应结果
     */
    @PostMapping("/query-task")
    @Operation(summary = "查询语音合成任务状态", description = "根据任务ID查询语音合成任务的执行状态和结果")
    public R<Object> queryTask(@Valid @RequestBody XfyunTtsQueryTaskPublicRequestDTO request) {
        log.debug("接收到查询TTS任务请求，taskId: {}", request.getHeader().getTaskId());
        try {
            Object response = xfyunTtsService.queryTask(request);
            return R.ok(response, "查询TTS任务成功");
        } catch (Exception e) {
            log.error("查询TTS任务失败: {}", e.getMessage(), e);
            return R.failed("查询TTS任务失败: " + e.getMessage());
        }
    }
}