package com.fytec.controller.external;

import com.fytec.aspect.annotation.ExternalServiceLog;
import com.fytec.constant.ExternalConstants;
import com.fytec.dto.external.XfyunRaasrGetResultRequestDTO;
import com.fytec.dto.external.XfyunRaasrUploadRequestDTO;
import com.fytec.service.external.XfyunRaasrService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * 讯飞录音文件转写控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/raasr")
@Tag(name = "讯飞录音文件转写", description = "讯飞录音文件转写API接口")
public class XfyunRaasrController {

    private final XfyunRaasrService xfyunRaasrService;

    /**
     * 上传录音文件进行转写（JSON模式）
     *
     * @param request 上传请求参数
     * @return 上传响应结果
     */
    @PostMapping("/upload")
    @Operation(summary = "上传录音文件进行转写（JSON模式）", description = "通过JSON参数上传录音文件信息到讯飞服务器进行语音转写，支持URL外链模式")
    @ExternalServiceLog(
            serviceType = ExternalConstants.ServiceType.ASR_FILE,
            contents = @ExternalServiceLog.Path2Type(
                    contentPath = "$[0].fileName",
                    processType = ExternalServiceLog.FieldProcessType.MULTIPART_FILE_AUDIO_DURATION
            ),
            dataMode = ExternalServiceLog.DataMode.REQUEST
    )
    public R<Object> uploadFile(@Valid @RequestBody XfyunRaasrUploadRequestDTO request) {
        log.debug("接收到录音文件转写上传请求，文件名: {}, 文件大小: {}, 时长: {}ms",
                request.getFileName(), request.getFileSize(), request.getDuration());
        try {
            Object response = xfyunRaasrService.uploadFile(request);
            return R.ok(response, "录音文件上传成功");
        } catch (Exception e) {
            log.error("录音文件上传失败: {}", e.getMessage(), e);
            return R.failed("录音文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传录音文件进行转写（文件流模式）
     *
     * @param file 音频文件
     * @param fileName 文件名（可选，如果不提供则使用上传文件的原始名称）
     * @param duration 音频时长（毫秒）
     * @param language 语种类型（可选）
     * @param callbackUrl 回调地址（可选）
     * @param hotWord 热词（可选）
     * @param candidate 多候选开关（可选）
     * @param roleType 角色分离开关（可选）
     * @param roleNum 说话人数（可选）
     * @param pd 领域个性化参数（可选）
     * @param standardWav 是否标准pcm/wav（可选）
     * @param languageType 语言识别模式选择（可选）
     * @param trackMode 按声道分轨转写模式（可选）
     * @param transLanguage 需要翻译的语种（可选）
     * @param transMode 翻译模式（可选）
     * @param engSegMax 控制分段的最大字数（可选）
     * @param engSegMin 控制分段的最小字数（可选）
     * @param engSegWeight 控制分段字数的权重（可选）
     * @param engSmoothproc 顺滑开关（可选）
     * @param engColloqproc 口语规整开关（可选）
     * @param engVadMdn 远近场模式（可选）
     * @param engVadMargin 首尾是否带静音信息（可选）
     * @param engRlang 针对粤语转写后的字体转换（可选）
     * @return 上传响应结果
     */
    @PostMapping("/upload-file")
    @Operation(summary = "上传录音文件进行转写（文件流模式）", description = "直接上传音频文件到讯飞服务器进行语音转写，支持多种音频格式和参数配置")
    @ExternalServiceLog(
            serviceType = ExternalConstants.ServiceType.ASR_FILE,
            contents = @ExternalServiceLog.Path2Type(
                    contentPath = "$[0].originalFilename",
                    processType = ExternalServiceLog.FieldProcessType.STRING_TEXT
            ),
            dataMode = ExternalServiceLog.DataMode.REQUEST
    )
    public R<Object> uploadFileStream(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam("duration") Long duration,
            @RequestParam(value = "language", required = false) String language,
            @RequestParam(value = "callbackUrl", required = false) String callbackUrl,
            @RequestParam(value = "hotWord", required = false) String hotWord,
            @RequestParam(value = "candidate", required = false) Short candidate,
            @RequestParam(value = "roleType", required = false) Short roleType,
            @RequestParam(value = "roleNum", required = false) Short roleNum,
            @RequestParam(value = "pd", required = false) String pd,
            @RequestParam(value = "standardWav", required = false) Integer standardWav,
            @RequestParam(value = "languageType", required = false) Integer languageType,
            @RequestParam(value = "trackMode", required = false) Short trackMode,
            @RequestParam(value = "transLanguage", required = false) String transLanguage,
            @RequestParam(value = "transMode", required = false) Short transMode,
            @RequestParam(value = "engSegMax", required = false) Integer engSegMax,
            @RequestParam(value = "engSegMin", required = false) Integer engSegMin,
            @RequestParam(value = "engSegWeight", required = false) Float engSegWeight,
            @RequestParam(value = "engSmoothproc", required = false) Boolean engSmoothproc,
            @RequestParam(value = "engColloqproc", required = false) Boolean engColloqproc,
            @RequestParam(value = "engVadMdn", required = false) Integer engVadMdn,
            @RequestParam(value = "engVadMargin", required = false) Integer engVadMargin,
            @RequestParam(value = "engRlang", required = false) Integer engRlang) {

        if (file.isEmpty()) {
            return R.failed("上传文件不能为空");
        }

        try {
            // 构建请求DTO
            XfyunRaasrUploadRequestDTO request = new XfyunRaasrUploadRequestDTO()
                    .setFileName(fileName != null ? fileName : file.getOriginalFilename())
                    .setFileSize(file.getSize())
                    .setDuration(duration)
                    .setAudioData(file.getBytes())
                    .setLanguage(language)
                    .setCallbackUrl(callbackUrl)
                    .setHotWord(hotWord)
                    .setCandidate(candidate)
                    .setRoleType(roleType)
                    .setRoleNum(roleNum)
                    .setPd(pd)
                    .setStandardWav(standardWav)
                    .setLanguageType(languageType)
                    .setTrackMode(trackMode)
                    .setTransLanguage(transLanguage)
                    .setTransMode(transMode)
                    .setEngSegMax(engSegMax)
                    .setEngSegMin(engSegMin)
                    .setEngSegWeight(engSegWeight)
                    .setEngSmoothproc(engSmoothproc)
                    .setEngColloqproc(engColloqproc)
                    .setEngVadMdn(engVadMdn)
                    .setEngVadMargin(engVadMargin)
                    .setEngRlang(engRlang);

            log.debug("接收到录音文件转写文件流上传请求，文件名: {}, 文件大小: {}, 时长: {}ms",
                    request.getFileName(), request.getFileSize(), request.getDuration());

            Object response = xfyunRaasrService.uploadFile(request);
            return R.ok(response, "录音文件上传成功");
        } catch (Exception e) {
            log.error("录音文件上传失败: {}", e.getMessage(), e);
            return R.failed("录音文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取转写结果
     *
     * @param request 获取结果请求参数
     * @return 转写结果响应
     */
    @PostMapping("/get-result")
    @Operation(summary = "获取转写结果", description = "根据订单ID获取录音文件转写结果")
    public R<Object> getResult(@Valid @RequestBody XfyunRaasrGetResultRequestDTO request) {
        log.debug("接收到获取转写结果请求，订单ID: {}", request.getOrderId());
        try {
            Object response = xfyunRaasrService.getResult(request);
            return R.ok(response, "获取转写结果成功");
        } catch (Exception e) {
            log.error("获取转写结果失败: {}", e.getMessage(), e);
            return R.failed("获取转写结果失败: " + e.getMessage());
        }
    }
}
