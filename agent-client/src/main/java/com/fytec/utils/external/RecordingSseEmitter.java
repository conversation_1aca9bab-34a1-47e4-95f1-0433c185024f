package com.fytec.utils.external;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

@Slf4j
public class RecordingSseEmitter extends SseEmitter {
    private final SseEmitter delegate;

    /** 记录所有发送过的消息 */
    @Getter
    private final List<Object> messages = new CopyOnWriteArrayList<>();

    /** 用户在外部注册的「完成」回调 */
    private Runnable completionCallback;

    /** 用户在外部注册的「错误」回调 */
    private Consumer<Throwable> errorCallback;

    /** 防止重复执行回调 */
    private final AtomicBoolean completed = new AtomicBoolean(false);

    public RecordingSseEmitter(SseEmitter original) {
        super(original.getTimeout());
        this.delegate = original;
        setupCallbacks();
    }

    /**
     * 在内部注册 SseEmitter 的 onTimeout/onError/onCompletion，
     * 以便在这些事件发生时执行我们自己的回调逻辑
     */
    private void setupCallbacks() {
        delegate.onTimeout(() -> {
            log.debug("RecordingSseEmitter 超时，消息数量: {}", messages.size());
            executeCompletionCallback();
        });

        delegate.onError(throwable -> {
            log.debug("RecordingSseEmitter 发生错误: {}，消息数量: {}",
                    throwable.getMessage(), messages.size());
            executeErrorCallback(throwable);
            executeCompletionCallback();
        });

        delegate.onCompletion(() -> {
            log.debug("RecordingSseEmitter 正常完成，消息数量: {}", messages.size());
            executeCompletionCallback();
        });
    }

    /**
     * 允许外部注册完成回调
     */
    public void onCompletion(Runnable callback) {
        this.completionCallback = callback;
        log.debug("已设置 SSE 完成回调");
    }

    /**
     * 允许外部注册错误回调
     */
    public void onRecordingError(Consumer<Throwable> callback) {
        this.errorCallback = callback;
        log.debug("已设置 SSE 错误回调");
    }


    @Override
    public void send(Object object, MediaType mediaType) throws IOException {
        messages.add(object);
        delegate.send(object, mediaType);
        log.debug("RecordingSseEmitter发送消息: {}", object);
    }

    @Override
    public void send(SseEventBuilder builder) throws IOException {
        messages.add(builder);
        delegate.send(builder);
        log.debug("RecordingSseEmitter发送事件构建器");
    }

    @Override
    public synchronized void send(Object object) throws IOException {
        log.debug("RecordingSseEmitter 发送默认消息: {}",
                object != null ? object.getClass().getSimpleName() : "null");
        messages.add(object);
        delegate.send(object);
    }

    @Override
    public void complete() {
        delegate.complete();
    }

    @Override
    public void completeWithError(Throwable ex) {
        delegate.completeWithError(ex);
    }

    /** 执行「正常完成」回调（只执行一次） */
    private void executeCompletionCallback() {
        if (completed.compareAndSet(false, true)) {
            log.info("首次执行 SSE 完成回调，消息总数: {}", messages.size());
            if (completionCallback != null) {
                try {
                    completionCallback.run();
                } catch (Exception e) {
                    log.error("执行 SSE 完成回调时发生错误: {}", e.getMessage(), e);
                }
            } else {
                log.debug("未设置 SSE 完成回调");
            }
        }
    }

    /** 执行「错误」回调 */
    private void executeErrorCallback(Throwable throwable) {
        if (errorCallback != null) {
            try {
                log.debug("执行 SSE 错误回调");
                errorCallback.accept(throwable);
            } catch (Exception e) {
                log.error("执行 SSE 错误回调时发生错误: {}", e.getMessage(), e);
            }
        } else {
            log.debug("未设置 SSE 错误回调");
        }
    }

    /** 手动强制完成（用于调试） */
    public void forceComplete() {
        log.debug("手动强制完成 SSE 流");
        complete();
    }

    /** 获取当前消息数量（用于调试） */
    public int getMessageCount() {
        return messages.size();
    }
}
