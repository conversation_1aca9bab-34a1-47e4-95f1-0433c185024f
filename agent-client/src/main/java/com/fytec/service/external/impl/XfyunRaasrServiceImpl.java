package com.fytec.service.external.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fytec.config.XfyunRaasrProperties;
import com.fytec.constant.ExternalConstants;
import com.fytec.dto.external.XfyunRaasrGetResultRequestDTO;
import com.fytec.dto.external.XfyunRaasrUploadRequestDTO;
import com.fytec.service.external.XfyunRaasrService;
import com.fytec.utils.external.XfyunSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 讯飞录音文件转写服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XfyunRaasrServiceImpl implements XfyunRaasrService {

    private final XfyunRaasrProperties xfyunRaasrProperties;

    @Override
    public Object uploadFile(XfyunRaasrUploadRequestDTO request) {
        if (!xfyunRaasrProperties.isAvailable()) {
            throw new IllegalStateException("讯飞录音文件转写服务不可用，请检查配置");
        }

        try {
            // 处理请求参数，赋予默认值
            processUploadRequestDefaults(request);

            // 生成时间戳和签名
            long ts = System.currentTimeMillis() / 1000;
            String signa = XfyunSignUtil.generateRaasrSigna(
                    xfyunRaasrProperties.getAppId(), 
                    ts, 
                    xfyunRaasrProperties.getApiSecret()
            );

            // 构建请求参数
            Map<String, Object> params = buildUploadParams(request, ts, signa);

            // 构建完整的URL
            String fullUrl = buildUrlWithParams(xfyunRaasrProperties.getUploadUrl(), params);
            log.debug("录音文件转写上传请求URL: {}", fullUrl);

            // 判断是否为音频流模式
            boolean isAudioStreamMode = request.getAudioData() != null && request.getAudioData().length > 0;

            // 发送HTTP POST请求
            HttpResponse response;
            if (isAudioStreamMode) {
                // 音频流模式：音频文件放在body中，设置Content-Type为application/octet-stream
                log.debug("使用音频流模式上传，音频数据大小: {} bytes", request.getAudioData().length);
                response = HttpUtil.createPost(fullUrl)
                        .header("Content-Type", "application/octet-stream")
                        .body(request.getAudioData())
                        .timeout(xfyunRaasrProperties.getConnectTimeout())
                        .setReadTimeout(xfyunRaasrProperties.getReadTimeout())
                        .execute();
            } else {
                // URL参数模式：参数拼接在URL中
                log.debug("使用URL参数模式上传");
                response = HttpUtil.createPost(fullUrl)
                        .header("Content-Type", "application/json; charset=UTF-8")
                        .header("Chunked", "false")
                        .timeout(xfyunRaasrProperties.getConnectTimeout())
                        .setReadTimeout(xfyunRaasrProperties.getReadTimeout())
                        .execute();
            }

            try (response) {

                if (!response.isOk()) {
                    log.error("录音文件转写上传失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                    throw new RuntimeException("录音文件转写上传失败: " + response.getStatus());
                }

                String responseBody = response.body();
                log.debug("录音文件转写上传响应: {}", responseBody);

                JSONObject responseObj = JSONUtil.parseObj(responseBody);
                
                // 检查响应中的错误码
                String code = responseObj.getStr("code");
                if (!"000000".equals(code)) {
                    String errorMsg = responseObj.getStr("descInfo", "未知错误");
                    log.error("录音文件转写上传业务失败，错误码: {}, 错误信息: {}", code, errorMsg);
                    throw new RuntimeException("录音文件转写上传失败: " + errorMsg);
                }

                return responseObj;
            }

        } catch (Exception e) {
            log.error("调用录音文件转写上传API失败", e);
            throw new RuntimeException("调用录音文件转写上传API失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Object getResult(XfyunRaasrGetResultRequestDTO request) {
        if (!xfyunRaasrProperties.isAvailable()) {
            throw new IllegalStateException("讯飞录音文件转写服务不可用，请检查配置");
        }

        try {
            // 处理请求参数，赋予默认值
            processGetResultRequestDefaults(request);

            // 生成时间戳和签名
            long ts = System.currentTimeMillis() / 1000;
            String signa = XfyunSignUtil.generateRaasrSigna(
                    xfyunRaasrProperties.getAppId(), 
                    ts, 
                    xfyunRaasrProperties.getApiSecret()
            );

            // 构建请求参数
            Map<String, Object> params = buildGetResultParams(request, ts, signa);

            // 构建完整的URL
            String fullUrl = buildUrlWithParams(xfyunRaasrProperties.getGetResultUrl(), params);
            log.debug("录音文件转写获取结果请求URL: {}", fullUrl);

            // 发送HTTP GET请求
            try (HttpResponse response = HttpUtil.createGet(fullUrl)
                    .header("Content-Type", "multipart/form-data")
                    .timeout(xfyunRaasrProperties.getConnectTimeout())
                    .setReadTimeout(xfyunRaasrProperties.getReadTimeout())
                    .execute()) {

                if (!response.isOk()) {
                    log.error("录音文件转写获取结果失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                    throw new RuntimeException("录音文件转写获取结果失败: " + response.getStatus());
                }

                String responseBody = response.body();
                log.debug("录音文件转写获取结果响应: {}", responseBody);

                JSONObject responseObj = JSONUtil.parseObj(responseBody);
                
                // 检查响应中的错误码
                String code = responseObj.getStr("code");
                if (!"000000".equals(code)) {
                    String errorMsg = responseObj.getStr("descInfo", "未知错误");
                    log.error("录音文件转写获取结果业务失败，错误码: {}, 错误信息: {}", code, errorMsg);
                    throw new RuntimeException("录音文件转写获取结果失败: " + errorMsg);
                }

                return responseObj;
            }

        } catch (Exception e) {
            log.error("调用录音文件转写获取结果API失败", e);
            throw new RuntimeException("调用录音文件转写获取结果API失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理上传请求的默认值
     */
    private void processUploadRequestDefaults(XfyunRaasrUploadRequestDTO request) {
        if (request.getLanguage() == null) {
            request.setLanguage(ExternalConstants.XfyunRaasrDefaults.DEFAULT_LANGUAGE);
        }
        if (request.getCandidate() == null) {
            request.setCandidate(ExternalConstants.XfyunRaasrDefaults.DEFAULT_CANDIDATE);
        }
        if (request.getRoleType() == null) {
            request.setRoleType(ExternalConstants.XfyunRaasrDefaults.DEFAULT_ROLE_TYPE);
        }
        if (request.getRoleNum() == null) {
            request.setRoleNum(ExternalConstants.XfyunRaasrDefaults.DEFAULT_ROLE_NUM);
        }
        if (request.getAudioMode() == null) {
            request.setAudioMode(ExternalConstants.XfyunRaasrDefaults.DEFAULT_AUDIO_MODE);
        }
        if (request.getStandardWav() == null) {
            request.setStandardWav(ExternalConstants.XfyunRaasrDefaults.DEFAULT_STANDARD_WAV);
        }
        if (request.getLanguageType() == null) {
            request.setLanguageType(ExternalConstants.XfyunRaasrDefaults.DEFAULT_LANGUAGE_TYPE);
        }
        if (request.getTrackMode() == null) {
            request.setTrackMode(ExternalConstants.XfyunRaasrDefaults.DEFAULT_TRACK_MODE);
        }
        if (request.getTransMode() == null) {
            request.setTransMode(ExternalConstants.XfyunRaasrDefaults.DEFAULT_TRANS_MODE);
        }
        if (request.getEngSmoothproc() == null) {
            request.setEngSmoothproc(ExternalConstants.XfyunRaasrDefaults.DEFAULT_ENG_SMOOTHPROC);
        }
        if (request.getEngColloqproc() == null) {
            request.setEngColloqproc(ExternalConstants.XfyunRaasrDefaults.DEFAULT_ENG_COLLOQPROC);
        }
        if (request.getEngVadMdn() == null) {
            request.setEngVadMdn(ExternalConstants.XfyunRaasrDefaults.DEFAULT_ENG_VAD_MDN);
        }
        if (request.getEngVadMargin() == null) {
            request.setEngVadMargin(ExternalConstants.XfyunRaasrDefaults.DEFAULT_ENG_VAD_MARGIN);
        }
        if (request.getEngRlang() == null) {
            request.setEngRlang(ExternalConstants.XfyunRaasrDefaults.DEFAULT_ENG_RLANG);
        }
    }

    /**
     * 处理获取结果请求的默认值
     */
    private void processGetResultRequestDefaults(XfyunRaasrGetResultRequestDTO request) {
        if (request.getResultType() == null) {
            request.setResultType(ExternalConstants.XfyunRaasrDefaults.DEFAULT_RESULT_TYPE);
        }
    }

    /**
     * 构建上传请求参数
     */
    private Map<String, Object> buildUploadParams(XfyunRaasrUploadRequestDTO request, long ts, String signa) {
        Map<String, Object> params = new HashMap<>();
        
        // 必须参数
        params.put("fileName", request.getFileName());
        params.put("fileSize", request.getFileSize());
        params.put("duration", request.getDuration());
        params.put("appId", xfyunRaasrProperties.getAppId());
        params.put("ts", ts);
        params.put("signa", signa);
        
        // 可选参数（只添加非空值）
        addIfNotNull(params, "language", request.getLanguage());
        addIfNotNull(params, "callbackUrl", request.getCallbackUrl());
        addIfNotNull(params, "hotWord", request.getHotWord());
        addIfNotNull(params, "candidate", request.getCandidate());
        addIfNotNull(params, "roleType", request.getRoleType());
        addIfNotNull(params, "roleNum", request.getRoleNum());
        addIfNotNull(params, "pd", request.getPd());
        addIfNotNull(params, "audioMode", request.getAudioMode());
        addIfNotNull(params, "audioUrl", request.getAudioUrl());
        addIfNotNull(params, "standardWav", request.getStandardWav());
        addIfNotNull(params, "languageType", request.getLanguageType());
        addIfNotNull(params, "trackMode", request.getTrackMode());
        addIfNotNull(params, "transLanguage", request.getTransLanguage());
        addIfNotNull(params, "transMode", request.getTransMode());
        addIfNotNull(params, "eng_seg_max", request.getEngSegMax());
        addIfNotNull(params, "eng_seg_min", request.getEngSegMin());
        addIfNotNull(params, "eng_seg_weight", request.getEngSegWeight());
        addIfNotNull(params, "eng_smoothproc", request.getEngSmoothproc());
        addIfNotNull(params, "eng_colloqproc", request.getEngColloqproc());
        addIfNotNull(params, "eng_vad_mdn", request.getEngVadMdn());
        addIfNotNull(params, "eng_vad_margin", request.getEngVadMargin());
        addIfNotNull(params, "eng_rlang", request.getEngRlang());
        
        return params;
    }

    /**
     * 构建获取结果请求参数
     */
    private Map<String, Object> buildGetResultParams(XfyunRaasrGetResultRequestDTO request, long ts, String signa) {
        Map<String, Object> params = new HashMap<>();
        
        // 必须参数
        params.put("orderId", request.getOrderId());
        params.put("appId", xfyunRaasrProperties.getAppId());
        params.put("ts", ts);
        params.put("signa", signa);
        
        // 可选参数
        addIfNotNull(params, "resultType", request.getResultType());
        
        return params;
    }

    /**
     * 添加非空参数到Map中
     */
    private void addIfNotNull(Map<String, Object> params, String key, Object value) {
        if (value != null) {
            params.put(key, value);
        }
    }

    /**
     * 构建带参数的URL
     */
    private String buildUrlWithParams(String baseUrl, Map<String, Object> params) {
        try {
            StringBuilder urlBuilder = new StringBuilder(baseUrl);
            if (!params.isEmpty()) {
                urlBuilder.append("?");
                boolean first = true;
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    if (!first) {
                        urlBuilder.append("&");
                    }
                    urlBuilder.append(entry.getKey())
                              .append("=")
                              .append(URLEncoder.encode(String.valueOf(entry.getValue()), StandardCharsets.UTF_8));
                    first = false;
                }
            }
            return urlBuilder.toString();
        } catch (Exception e) {
            log.error("构建URL失败", e);
            throw new RuntimeException("构建URL失败: " + e.getMessage(), e);
        }
    }
}
