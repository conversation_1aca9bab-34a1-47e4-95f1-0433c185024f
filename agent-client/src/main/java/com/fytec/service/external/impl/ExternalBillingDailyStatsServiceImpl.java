package com.fytec.service.external.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fytec.constant.ExternalConstants;
import com.fytec.dto.external.ExternalBillingDailyStatsQueryDTO;
import com.fytec.dto.external.ExternalBillingDailyStatsResultDTO;
import com.fytec.entity.external.ExternalBillingDailyStats;
import com.fytec.entity.external.ExternalUseLog;
import com.fytec.mapper.external.ExternalBillingDailyStatsMapper;
import com.fytec.service.external.ExternalBillingDailyStatsService;
import com.fytec.service.external.ExternalUseLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 能力服务每日使用统计表 Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ExternalBillingDailyStatsServiceImpl
        extends ServiceImpl<ExternalBillingDailyStatsMapper, ExternalBillingDailyStats>
        implements ExternalBillingDailyStatsService {

    private final ExternalUseLogService externalUseLogService;

    @Override
    public void generateDailyStats(LocalDate statsDate) {
        if (statsDate == null) {
            throw new IllegalArgumentException("统计日期不能为空");
        }

        log.info("开始生成日期 {} 的每日统计数据", statsDate);

        // 1. 计算查询时间范围（当天 00:00:00 到 23:59:59）
        LocalDateTime startTime = statsDate.atStartOfDay();
        LocalDateTime endTime = statsDate.atTime(23, 59, 59);

        // 2. 查询当天的所有使用日志
        List<ExternalUseLog> logs = externalUseLogService.findByCallTimeBetween(startTime, endTime);
        log.info("查询到 {} 条使用日志记录", logs.size());

        if (logs.isEmpty()) {
            log.info("日期 {} 没有使用日志记录，跳过统计", statsDate);
            return;
        }

        // 3. 按 serviceType 分组统计
        Map<String, List<ExternalUseLog>> groupedLogs = logs.stream()
                .filter(log -> log.getServiceType() != null)
                .collect(Collectors.groupingBy(ExternalUseLog::getServiceType));


        // 4. 为每个服务类型生成统计数据
        for (Map.Entry<String, List<ExternalUseLog>> entry : groupedLogs.entrySet()) {
            String serviceType = entry.getKey();
            List<ExternalUseLog> serviceLogs = entry.getValue();

            try {
                // 根据服务类型计算统计数据
                ExternalBillingDailyStats stats = calculateStats(serviceType, serviceLogs, statsDate);
                // Upsert 操作
                upsertStats(stats);
            } catch (Exception e) {
                log.error("处理服务类型 {} 的统计数据时发生错误", serviceType, e);
                // 继续处理其他服务类型，不中断整个流程
            }
        }

    }

    @Override
    public Page<ExternalBillingDailyStatsResultDTO> queryBillingStatsPage(
            Page<ExternalBillingDailyStatsResultDTO> page,
            ExternalBillingDailyStatsQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }

        if (queryDTO.getStartDate() == null || queryDTO.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        if (queryDTO.getStartDate().isAfter(queryDTO.getEndDate())) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        // 调用 Mapper 进行分页查询
        List<ExternalBillingDailyStatsResultDTO> records = baseMapper.queryBillingStatsPage(page, queryDTO);
        page.setRecords(records);
        return page;
    }

    /**
     * 根据服务类型和日志列表计算统计数据
     *
     * @param serviceType 服务类型
     * @param logs 日志列表
     * @param statsDate 统计日期
     * @return 统计数据
     */
    private ExternalBillingDailyStats calculateStats(String serviceType, List<ExternalUseLog> logs, LocalDate statsDate) {
        ExternalBillingDailyStats stats = new ExternalBillingDailyStats();
        stats.setServiceCode(serviceType);
        stats.setStatsDate(statsDate);
        // 调用次数
        stats.setUseCount((long) logs.size());

        // 根据服务类型统计不同字段
        switch (serviceType) {
            case "TTS_PREMIUM_SHORT":
            case "ASR_FILE":
            case "OCR_EDUCATION":
            case "OCR_GENERAL":
                // 统计 calculateCount
                long totalCalculateCount = logs.stream()
                        .mapToLong(log -> Optional.ofNullable(log.getCalculateCount()).orElse(0))
                        .sum();
                stats.setServiceCount(totalCalculateCount);
                log.debug("服务类型 {} 统计折算次数: {}", serviceType, totalCalculateCount);
                break;

            case "TTS_PREMIUM_LONG":
                // 统计 characterCount
                long totalCharacterCount = logs.stream()
                        .mapToLong(log -> Optional.ofNullable(log.getCharacterCount()).orElse(0))
                        .sum();
                stats.setServiceWordCount(totalCharacterCount);
                log.debug("服务类型 {} 统计字符数: {}", serviceType, totalCharacterCount);
                break;

            case "ASR_REALTIME":
                // 统计 durationSeconds，转换为分钟
                long totalDurationSeconds = logs.stream()
                        .mapToLong(log -> Optional.ofNullable(log.getDurationSeconds()).orElse(0))
                        .sum();
                // 转换为分钟
                stats.setUsageDurationMinutes(totalDurationSeconds / 60);
                log.debug("服务类型 {} 统计时长: {} 秒 ({} 分钟)", serviceType, totalDurationSeconds, totalDurationSeconds / 60);
                break;

            default:
                log.warn("未知的服务类型: {}, 仅统计调用次数", serviceType);
                break;
        }

        return stats;
    }

    /**
     * 执行 upsert 操作：如果记录存在则更新，不存在则插入
     *
     * @param stats 统计数据
     */
    private void upsertStats(ExternalBillingDailyStats stats) {
        // 查询是否存在相同日期和服务类型的记录
        ExternalBillingDailyStats existingStats = lambdaQuery()
                .eq(ExternalBillingDailyStats::getStatsDate, stats.getStatsDate())
                .eq(ExternalBillingDailyStats::getServiceCode, stats.getServiceCode())
                .one();

        if (existingStats != null) {
            // 更新现有记录
            stats.setId(existingStats.getId());
            stats.setCreateBy(existingStats.getCreateBy());
            stats.setCreateTime(existingStats.getCreateTime());
            this.updateById(stats);
            log.info("更新统计数据: 日期={}, 服务类型={}, 服务次数={}",
                    stats.getStatsDate(), stats.getServiceCode(), stats.getServiceCount());
        } else {
            // 插入新记录
            this.save(stats);
            log.info("插入统计数据: 日期={}, 服务类型={}, 服务次数={}",
                    stats.getStatsDate(), stats.getServiceCode(), stats.getServiceCount());
        }
    }
}