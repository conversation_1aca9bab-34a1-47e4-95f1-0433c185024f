package com.fytec.service.external;

import com.fytec.dto.external.XfyunRaasrGetResultRequestDTO;
import com.fytec.dto.external.XfyunRaasrUploadRequestDTO;

/**
 * 讯飞录音文件转写服务接口
 *
 * <AUTHOR>
 */
public interface XfyunRaasrService {

    /**
     * 上传录音文件进行转写
     *
     * @param request 上传请求参数
     * @return 上传响应结果
     */
    Object uploadFile(XfyunRaasrUploadRequestDTO request);

    /**
     * 获取转写结果
     *
     * @param request 获取结果请求参数
     * @return 转写结果响应
     */
    Object getResult(XfyunRaasrGetResultRequestDTO request);
}
