package com.fytec.aspect;

import com.alibaba.fastjson2.JSONPath;
import com.fytec.aspect.annotation.ExternalServiceLog;
import com.fytec.constant.ExternalConstants;
import com.fytec.entity.external.ExternalUseLog;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.service.external.ExternalUseLogService;
import com.fytec.utils.AudioUtil;
import com.fytec.utils.BillingCalculationUtil;
import com.fytec.utils.external.ReusableMultipartFile;
import com.fytec.utils.external.RecordingSseEmitter;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.Base64;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 外部服务调用监控切面
 * 拦截带有@ExternalServiceLog注解的方法，自动记录调用日志和计费信息到ExternalUseLog实体
 *
 * 功能特性：
 * 1. 支持多种参数类型：String、MultipartFile、File、InputStream、DTO等
 * 2. 自动计算Token数量（中文2字符=1Token，英文4字符=1Token）
 * 3. 支持多种计费策略：按字符数、按Token数、按时长、按请求次数等
 * 4. 异步记录日志，不影响业务性能
 * 5. 完善的异常处理机制
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ExternalServiceLogAspect {

    private final ExternalUseLogService externalUseLogService;

    /**
     * 环绕通知：拦截外部服务调用
     */
    @Around("@annotation(externalServiceLog)")
    public Object around(ProceedingJoinPoint pjp, ExternalServiceLog externalServiceLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        String sessionId = generateSessionId();
        Object result = null;
        String errorMsg = null;
        String serviceStatus = "SUCCESS";
        Object[] processedArgs = null;

        try {
            log.debug("开始监控外部服务调用: {}, 会话ID: {}",
                     externalServiceLog.serviceType().getDescription(), sessionId);

            // 预处理参数（如果需要）
            processedArgs = externalServiceLog.needDecorateMultipartFile()
                    ? preProcessArgs(pjp)
                    : pjp.getArgs();

            // 执行目标方法
            result = pjp.proceed(processedArgs);

            log.debug("外部服务调用成功: {}, 耗时: {}ms",
                     externalServiceLog.serviceType().getDescription(),
                     System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            serviceStatus = "FAILED";
            errorMsg = e.getMessage();
            log.error("外部服务调用失败: {}, 错误: {}",
                     externalServiceLog.serviceType().getDescription(), e.getMessage(), e);
            throw e;
        } finally {
            // 记录监控日志，使用预处理后的参数
            final Object[] finalProcessedArgs = processedArgs != null ? processedArgs : pjp.getArgs();
            // 处理SSE返回值，如果需要装饰Emitter TODO
//            if (result instanceof SseEmitter && externalServiceLog.needDecorateEmitter()) {
//                result = wrapSseEmitterForAudioMonitoring((SseEmitter) result, externalServiceLog,
//                        sessionId, startTime, pjp);
//            }
            if (externalServiceLog.async()) {
                recordLogAsync(pjp, finalProcessedArgs, externalServiceLog, result, startTime,
                              sessionId, serviceStatus, errorMsg);
            } else {
                recordLog(pjp, finalProcessedArgs, externalServiceLog, result, startTime,
                         sessionId, serviceStatus, errorMsg);
            }
        }



        return result;
    }

    /**
     * 预处理请求参数，防止消耗流数据
     */
    private static Object[] preProcessArgs(ProceedingJoinPoint pjp) throws IOException {
        Object[] args = pjp.getArgs();
        Object[] newArgs = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg instanceof MultipartFile original && !(arg instanceof ReusableMultipartFile)) {
                newArgs[i] = new ReusableMultipartFile(original);
            } else {
                newArgs[i] = arg;
            }
        }
        return newArgs;
    }

    /**
     * 异步记录日志
     */
    public void recordLogAsync(ProceedingJoinPoint pjp,
                               Object[] args,
                               ExternalServiceLog annotation,
                               Object result,
                               long startTime,
                               String sessionId,
                               String serviceStatus,
                               String errorMsg) {
        CompletableFuture.runAsync(() -> {
            try {
                recordLog(pjp, args, annotation, result, startTime, sessionId, serviceStatus, errorMsg);
            } catch (Exception e) {
                log.error("异步记录外部服务日志失败: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 记录监控日志
     */
    private void recordLog(ProceedingJoinPoint pjp,
                            Object[] args,
                            ExternalServiceLog annotation,
                            Object result,
                            long startTime,
                            String sessionId,
                            String serviceStatus,
                            String errorMsg) {
        try {
            // 解析内容信息，使用预处理后的参数
            ContentInfo contentInfo = parseContent(args, annotation, result);

            // 获取用户和客户端信息
            UserInfo userInfo = getCurrentUserInfo();

            // 计算计费信息
            BillingInfo billingInfo = calculateBilling(contentInfo, annotation);

            // 构建日志记录
            ExternalUseLog logRecord = buildLogRecord(annotation, contentInfo, billingInfo,
                                                     userInfo, startTime, sessionId,
                                                     serviceStatus, errorMsg, pjp);

            // 保存到数据库
            externalUseLogService.saveExternalUseLog(logRecord);

            log.debug("外部服务监控日志记录成功: {}, 会话ID: {}, 数据模式: {}",
                     annotation.serviceType().getDescription(), sessionId, annotation.dataMode().getDescription());

        } catch (Exception e) {
            log.error("记录外部服务监控日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析内容信息, 只记录内容和时长
     */
    private ContentInfo parseContent(Object[] args, ExternalServiceLog annotation, Object result) {
        ContentInfo info = new ContentInfo();

        // 根据数据模式处理不同的数据源
        switch (annotation.dataMode()) {
            case REQUEST:
                parseRequestContent(args, annotation, info);
                break;
            case RESPONSE:
                parseResponseContent(result, annotation, info);
                break;
        }

        return info;
    }

    /**
     * 解析请求内容
     * 完全依赖注解中的contentPaths进行内容提取，不再手动判断参数类型
     */
    private void parseRequestContent(Object[] args, ExternalServiceLog annotation, ContentInfo info) {
        if (args == null) {
            return;
        }
        try {
            // 使用JSONPath提取内容
            extractContentByPaths(args, annotation, info);
        } catch (Exception e) {
            log.warn("解析响应内容失败: {}", e.getMessage());
        }
    }

    /**
     * 解析响应内容
     */
    private void parseResponseContent(Object result, ExternalServiceLog annotation, ContentInfo info) {
        if (result == null) {
            return;
        }
        try {
            // 使用JSONPath提取内容
            extractContentByPaths(result, annotation, info);
        } catch (Exception e) {
            log.warn("解析响应内容失败: {}", e.getMessage());
        }
    }

    /**
     * 根据JSONPath提取内容（支持多个路径）
     * 对特殊类型进行预处理，然后使用JSONPath提取
     */
    private void extractContentByPaths(Object source, ExternalServiceLog annotation, ContentInfo info) {
        // 对特殊类型进行预处理
        ExternalServiceLog.Path2Type[] path2types = annotation.contents();
        try {
            // 提取对象内容
            for (ExternalServiceLog.Path2Type path2type : path2types) {
                Object targetField = extractFieldByPath(source, path2type.contentPath().trim());
                if(ExternalServiceLog.FieldProcessType.STRING_TEXT.equals(path2type.processType())) {
                    // 使用fastjson的JSONPath提取内容
                    String content = extractStringFromResult(targetField);
                    handleStringText(info, content);
                }else if(ExternalServiceLog.FieldProcessType.MULTIPART_FILE_AUDIO_DURATION.equals(path2type.processType())
                        && targetField instanceof MultipartFile) {
                    handleMultipartFile(info, (MultipartFile) targetField);
                }else if(ExternalServiceLog.FieldProcessType.SSE_BASE64_AUDIO_DURATION.equals(path2type.processType())) {
                    handleSseBase64AudioDuration(info, targetField, path2type.contentPath());
                }else if(ExternalServiceLog.FieldProcessType.BASE64_AUDIO_DURATION.equals(path2type.processType())) {
                    handleBase64AudioDuration(info, targetField);
                }else if(ExternalServiceLog.FieldProcessType.FILE_AUDIO_DURATION.equals(path2type.processType())) {
                    handleFileAudioDuration(info, targetField);
                }
                // TODO 其他类型数据提取
            }
        } catch (Exception e) {
            log.warn("根据path和type获取内容失败: {}", e.getMessage());
        }
    }

    /**
     * 根据路径提取字段，支持多种数据类型
     */
    private Object extractFieldByPath(Object source, String path) {
        if (source == null || path == null || path.trim().isEmpty()) {
            return null;
        }
        if (source instanceof SseEmitter) {
            return source;
        }
        try {
            // 首先尝试使用 fastjson 的 JSONPath 处理
            return JSONPath.eval(source, path);
        } catch (Exception e) {
            log.debug("使用JSONPath提取字段失败，尝试转换为JSON字符串: {}", e.getMessage());
            try {
                // 如果JSONPath失败，先转换为JSON字符串，然后再使用JSONPath
                String jsonStr = com.alibaba.fastjson2.JSON.toJSONString(source);
                Object jsonObj = com.alibaba.fastjson2.JSON.parse(jsonStr);
                return JSONPath.eval(jsonObj, path);

            } catch (Exception e2) {
                log.debug("转换为JSON字符串后仍然失败: {}", e2.getMessage());
                return null;
            }
        }
    }

    /**
     * 从JSONPath结果中提取字符串内容
     */
    private String extractStringFromResult(Object result) {
        return switch (result) {
            case null -> null;
            case String s -> s;
            case List<?> list -> list.stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.joining(" "));
            default -> result.toString();
        };
    }

    /**
     * 处理MultipartFile
     */
    private void handleMultipartFile(ContentInfo info, MultipartFile file) {
        info.setFileName(file.getOriginalFilename());

        // 根据文件类型判断是否为音频文件
        String contentType = file.getContentType();
        String fileName = file.getOriginalFilename();

        if ((contentType != null && contentType.startsWith("audio/")) ||
            AudioUtil.isAudioFile(fileName)) {
            // 使用AudioUtil计算音频时长
            int duration = AudioUtil.calculateAudioDurationSeconds(file);
            info.setDurationSeconds(duration);
            log.debug("音频文件 {} 时长: {} 秒", fileName, duration);
        }
    }

    /**
     * 处理SSE Base64音频时长
     */
    private void handleSseBase64AudioDuration(ContentInfo info, Object targetField, String path) {
        try {
            if (targetField instanceof SseEmitter) {
                // 检查是否是装饰过的RecordingSseEmitter
                if (targetField instanceof RecordingSseEmitter) {
                    RecordingSseEmitter recordingEmitter = (RecordingSseEmitter) targetField;

                    // 设置完成回调来处理音频时长计算
                    recordingEmitter.onCompletion(() -> {
                        try {
                            log.debug("SSE流完成，开始处理音频时长计算");

                            // 从消息列表中提取base64音频数据
                            List<Object> messages = recordingEmitter.getMessages();
                            Object audioData = JSONPath.eval(messages, path);
                            List<String> base64DataList = extractBase64AudioData(audioData);

                            if (!base64DataList.isEmpty()) {
                                // 合并所有base64数据并计算音频时长
                                byte[] mergedAudioBytes = mergeBase64AudioData(base64DataList);
                                if (mergedAudioBytes.length > 0) {
                                    int duration = calculateAudioDurationFromBytes(mergedAudioBytes, "mp3");

                                    // 更新ContentInfo对象
                                    synchronized (info) {
                                        info.setDurationSeconds(duration);
                                    }

                                    log.debug("SSE Base64音频时长计算完成: {} 秒, 数据片段数: {}", duration, base64DataList.size());
                                }
                            }
                        } catch (Exception e) {
                            log.error("SSE音频时长计算失败: {}", e.getMessage(), e);
                        }
                    });
                } else {
                    log.debug("SseEmitter未被装饰，无法监控SSE音频时长");
                }
            }
        } catch (Exception e) {
            log.warn("处理SSE Base64音频时长失败: {}", e.getMessage());
        }
    }

    /**
     * 处理Base64音频时长
     */
    private void handleBase64AudioDuration(ContentInfo info, Object targetField) {
        try {
            List<String> base64DataList = extractBase64AudioData(targetField);

            if (!base64DataList.isEmpty()) {
                // 合并所有base64数据并计算音频时长
                byte[] mergedAudioBytes = mergeBase64AudioData(base64DataList);
                if (mergedAudioBytes.length > 0) {
                    int duration = calculateAudioDurationFromBytes(mergedAudioBytes, "mp3");
                    info.setDurationSeconds(duration);
                    log.debug("Base64音频时长: {} 秒, 数据片段数: {}", duration, base64DataList.size());
                }
            }
        } catch (Exception e) {
            log.warn("处理Base64音频时长失败: {}", e.getMessage());
        }
    }

    /**
     * 处理文件音频时长
     */
    private void handleFileAudioDuration(ContentInfo info, Object targetField) {
        try {
            if (targetField instanceof byte[]) {
                byte[] audioBytes = (byte[]) targetField;
                int duration = calculateAudioDurationFromBytes(audioBytes, "mp3");
                info.setDurationSeconds(duration);
                log.debug("文件音频时长: {} 秒, 文件大小: {} 字节", duration, audioBytes.length);
            }
        } catch (Exception e) {
            log.warn("处理文件音频时长失败: {}", e.getMessage());
        }
    }

    /**
     * 追加文本内容到ContentInfo
     */
    private void handleStringText(ContentInfo info, String content) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }

        // 累加文本内容
        if (info.getTextContent() != null) {
            info.setTextContent(info.getTextContent() + content);
            info.setCharacterCount(info.getCharacterCount() + content.length());
        } else {
            info.setTextContent(content);
            info.setCharacterCount(content.length());
        }

        log.debug("追加文本内容统计: {}", BillingCalculationUtil.getTextStatistics(content));
    }

    /**
     * 从各种数据结构中提取Base64音频数据
     */
    private List<String> extractBase64AudioData(Object data) {
        List<String> base64DataList = new ArrayList<>();

        if (data == null) {
            return base64DataList;
        }

        try {
            if (data instanceof String base64Data) {
                // 单个base64字符串
                if (!base64Data.trim().isEmpty()) {
                    base64DataList.add(base64Data.trim());
                }
            } else if (data instanceof List<?> dataList) {
                // 处理消息列表（来自RecordingSseEmitter）或base64数组
                for (Object item : dataList) {
                    if (item instanceof String base64Data) {
                        if (!base64Data.trim().isEmpty()) {
                            base64DataList.add(base64Data.trim());
                        }
                    } else if (item instanceof SseEmitter.SseEventBuilder) {
                        // 从SseEventBuilder中提取音频数据
                        // 这里需要根据实际的数据结构来提取
                        // 暂时跳过，因为SseEventBuilder的数据提取比较复杂
                        log.debug("跳过SseEventBuilder类型的数据提取");
                    } else {
                        // 尝试从复杂对象中提取audio字段
                        String audioData = extractAudioFromObject(item);
                        if (audioData != null && !audioData.trim().isEmpty()) {
                            base64DataList.add(audioData.trim());
                        }
                    }
                }
            } else {
                // 尝试从复杂对象中提取audio字段
                String audioData = extractAudioFromObject(data);
                if (audioData != null && !audioData.trim().isEmpty()) {
                    base64DataList.add(audioData.trim());
                }
            }
        } catch (Exception e) {
            log.warn("提取Base64音频数据失败: {}", e.getMessage());
        }

        return base64DataList;
    }

    /**
     * 从对象中提取音频数据
     */
    private String extractAudioFromObject(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            // 尝试使用JSONPath提取audio字段
            Object audioData = com.alibaba.fastjson2.JSONPath.eval(obj, "$.data.audio");
            if (audioData instanceof String) {
                return (String) audioData;
            }

            // 尝试其他可能的路径
            audioData = com.alibaba.fastjson2.JSONPath.eval(obj, "$.audio");
            if (audioData instanceof String) {
                return (String) audioData;
            }
        } catch (Exception e) {
            log.debug("从对象中提取音频数据失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 合并多个Base64音频数据
     */
    private byte[] mergeBase64AudioData(List<String> base64DataList) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            for (String base64Data : base64DataList) {
                if (base64Data != null && !base64Data.trim().isEmpty()) {
                    byte[] audioBytes = Base64.getDecoder().decode(base64Data.trim());
                    outputStream.write(audioBytes);
                }
            }
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("合并Base64音频数据失败: {}", e.getMessage());
            return new byte[0];
        }
    }

    /**
     * 从字节数组计算音频时长
     */
    private int calculateAudioDurationFromBytes(byte[] audioBytes, String format) {
        if (audioBytes == null || audioBytes.length == 0) {
            return 0;
        }

        Path tempFile = null;
        try {
            // 创建临时文件
            String suffix = "." + (format != null ? format : "mp3");
            tempFile = Files.createTempFile("audio_duration_", suffix);

            // 写入音频数据
            Files.write(tempFile, audioBytes);

            // 计算音频时长
            int duration = AudioUtil.calculateAudioDurationSeconds(tempFile.toFile());
            log.debug("从字节数组计算音频时长: {} 秒, 数据大小: {} 字节", duration, audioBytes.length);

            return duration;
        } catch (Exception e) {
            log.error("从字节数组计算音频时长失败: {}", e.getMessage());
            return 0;
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 计算计费信息
     * 根据ServiceType和BILLING_CALC_RULE.md规则进行精确的计费计算
     */
    private BillingInfo calculateBilling(ContentInfo contentInfo,
                                       ExternalServiceLog annotation) {
        BillingInfo billing = new BillingInfo();
        ExternalConstants.ServiceType serviceType = annotation.serviceType();

        int billingCount;
        String billingDescription = switch (serviceType) {
            case TTS_PREMIUM_SHORT -> {
                // 精品语音合成（短文本）：按音频时长5秒/次计费
                billingCount = BillingCalculationUtil.calculateTtsPremiumShortBilling(contentInfo.getDurationSeconds());
                yield String.format("短文本TTS计费 - 音频时长: %d 秒, 计费次数: %d",
                        contentInfo.getDurationSeconds(), billingCount);
            }
            case TTS_PREMIUM_LONG -> {
                // 精品语音合成（长文本）：按字符数计费
                billingCount = BillingCalculationUtil.calculateTtsPremiumLongBilling(contentInfo.getTextContent());
                yield String.format("长文本TTS计费 - 字符数: %d, 计费次数: %d",
                        contentInfo.getCharacterCount(), billingCount);
            }
            case ASR_FILE -> {
                // 语音识别（文件）：每5秒记为1次
                billingCount = BillingCalculationUtil.calculateAsrFileBilling(contentInfo.getDurationSeconds());
                yield String.format("文件ASR计费 - 音频时长: %d 秒, 计费次数: %d",
                        contentInfo.getDurationSeconds(), billingCount);
            }
            case ASR_REALTIME -> {
                // 语音识别（实时）：按秒计费
                billingCount = BillingCalculationUtil.calculateAsrRealtimeBilling(contentInfo.getDurationSeconds());
                yield String.format("实时ASR计费 - 音频时长: %d 秒, 计费次数: %d",
                        contentInfo.getDurationSeconds(), billingCount);
            }
            case OCR_EDUCATION -> {
                // 教育场景文字识别：按Token计费
                billingCount = BillingCalculationUtil.calculateOcrEducationBilling(contentInfo.getTextContent());
                yield String.format("教育OCR计费 - 字符数: %d, 计费次数: %d",
                        contentInfo.getCharacterCount(), billingCount);
            }
            case OCR_GENERAL -> {
                // 通用场景文字识别：按Token计费
                billingCount = BillingCalculationUtil.calculateOcrGeneralBilling(contentInfo.getTextContent());

                yield String.format("通用OCR计费 - 字符数: %d, 计费次数: %d",
                        contentInfo.getCharacterCount(), billingCount);
            }
        };
        billing.setCalculateCount(billingCount);
        log.debug(billingDescription);

        return billing;
    }

    /**
     * 获取当前用户信息
     * 使用StpClientUserUtil获取用户ID和客户端ID
     */
    private UserInfo getCurrentUserInfo() {
        UserInfo userInfo = new UserInfo();
        // 使用StpClientUserUtil获取用户ID
        userInfo.setUserId(StpClientUserUtil.getLoginIdAsString());
        // 使用StpClientUserUtil获取客户端ID
        userInfo.setClientId(StpClientUserUtil.getClientId());
        log.debug("获取用户信息成功 - userId: {}, clientId: {}", userInfo.getUserId(), userInfo.getClientId());
        return userInfo;
    }

    /**
     * 构建日志记录
     */
    private ExternalUseLog buildLogRecord(ExternalServiceLog annotation,
                                        ContentInfo contentInfo,
                                        BillingInfo billingInfo,
                                        UserInfo userInfo,
                                        long startTime,
                                        String sessionId,
                                        String serviceStatus,
                                        String errorMsg,
                                        ProceedingJoinPoint pjp) {
        ExternalUseLog log = new ExternalUseLog();

        // 基本信息
        log.setId(sessionId);
        log.setCallTime(LocalDateTime.now());
        log.setCreateTime(LocalDateTime.now());
        log.setUpdateTime(LocalDateTime.now());

        // 用户信息
        log.setUserId(userInfo.getUserId());
        log.setClientId(userInfo.getClientId());

        // 方法信息（类名+方法名）
        String className = pjp.getTarget().getClass().getSimpleName();
        String methodName = pjp.getSignature().getName();
        log.setMethod(className + "." + methodName);

        // 服务信息
        log.setServiceStatus(serviceStatus);
        log.setServiceType(annotation.serviceType().getCode());

        // 内容信息
        log.setFileName(contentInfo.getFileName());
        log.setCharacterCount(contentInfo.getCharacterCount());
        log.setDurationSeconds(contentInfo.getDurationSeconds());
        log.setCalculateCount(billingInfo.getCalculateCount());

        // 备注信息
        StringBuilder remarks = new StringBuilder();
        remarks.append("服务类型: ").append(annotation.serviceType().getDescription());
        if (errorMsg != null) {
            remarks.append(", 错误信息: ").append(errorMsg);
        }
        log.setRemarks(remarks.toString());

        return log;
    }



    /**
     * 包装SseEmitter以监控音频时长
     */
    private SseEmitter wrapSseEmitterForAudioMonitoring(SseEmitter originalEmitter,
                                                       ExternalServiceLog annotation,
                                                       String sessionId,
                                                       long startTime,
                                                       ProceedingJoinPoint pjp) {
        RecordingSseEmitter recordingEmitter = new RecordingSseEmitter(originalEmitter);

        // 设置错误回调
        recordingEmitter.onError((throwable) -> {
            log.warn("SSE流发生错误，会话ID: {}, 错误: {}", sessionId, throwable.getMessage());
        });

        log.debug("已装饰SseEmitter用于音频时长监控，会话ID: {}", sessionId);
        return recordingEmitter;
    }



    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 内容信息类
     */
    @Getter
    @Setter
    private static class ContentInfo {
        private String textContent;
        private String fileName;
        private Integer characterCount = 0;
        private Integer tokenCount = 0;
        private Integer durationSeconds = 0;
    }

    /**
     * 计费信息类
     */
    @Getter
    @Setter
    private static class BillingInfo {
        private Integer calculateCount = 0;
    }

    /**
     * 用户信息类
     */
    @Getter
    @Setter
    private static class UserInfo {
        private String userId;
        private String clientId;
    }
}