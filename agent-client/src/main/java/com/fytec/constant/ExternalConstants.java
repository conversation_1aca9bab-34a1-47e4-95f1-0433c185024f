package com.fytec.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Optional;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 外部服务常量类
 * 定义易加AI智慧教育应用项目能力服务相关常量
 */
public class ExternalConstants {

    /**
     * 音频格式编码枚举
     */
    @Getter
    @AllArgsConstructor
    public enum AudioFormatEncoding {
        PCM("pcm", "raw", "未压缩的pcm格式音频"),
        WAV("wav", "raw", "wav格式音频（建议去掉头部）"),
        MP3("mp3", "lame", "mp3格式音频"),
        SPEEX("speex", "speex-wb;7", "讯飞定制speex格式音频(默认值)");

        private final String fileSuffix;
        private final String code;
        private final String description;

        // 文件格式到编码格式的映射
        private static final Map<String, String> FILE_FORMAT_TO_ENCODING_MAP =
                Arrays.stream(AudioFormatEncoding.values())
                        .collect(Collectors.toMap(AudioFormatEncoding::getFileSuffix, AudioFormatEncoding::getCode));

        /**
         * 根据文件格式获取对应的编码格式
         *
         * @param fileFormat 文件格式
         * @return 编码格式
         */
        public static Optional<String> getEncodingByFileFormat(String fileFormat) {
            return Optional.ofNullable(FILE_FORMAT_TO_ENCODING_MAP.get(fileFormat));
        }
    }



    // 服务状态常量
    public static final String SERVICE_STATUS_ACTIVE = "active";
    public static final String SERVICE_STATUS_INACTIVE = "inactive";
    public static final String SERVICE_STATUS_SUSPENDED = "suspended";

    // 服务调用结果状态
    public static final String CALL_STATUS_SUCCESS = "success";
    public static final String CALL_STATUS_FAILED = "failed";
    public static final String CALL_STATUS_PENDING = "pending";
    
    // 文档解析服务相关常量
    public static final String DOC_PARSE_INTERFACE = "/document/parseNew";
    public static final String DOC_PARSE_METHOD = "BaiduyunDocParserClient.parseDoc";
    public static final String LOG_TYPE_DOC_PARSE = "DOC_PARSE";
    
    // 计费相关常量
    public static final int BASE_CHARGE_COUNT = 1;
    public static final double SIZE_FEE_PER_100KB = 0.1;
    public static final int LARGE_FILE_THRESHOLD_MB = 5;
    public static final double LARGE_FILE_EXTRA_FEE = 0.5;
    
    // 文件类型额外费用
    public static final double PDF_EXTRA_FEE = 0.3;
    public static final double WORD_EXTRA_FEE = 0.2;
    public static final double PPT_EXTRA_FEE = 0.25;
    public static final double EXCEL_EXTRA_FEE = 0.15;

    /**
     * 讯飞TTS创建任务默认参数枚举
     */
    @Getter
    @AllArgsConstructor
    public enum XfyunTtsCreateDefaultParams {
        // TTS参数默认值
        LANGUAGE("language", "zh"),
        SPEED("speed", 50),
        VOLUME("volume", 50),
        PITCH("pitch", 50),
        RAM("ram", 0),
        RHY("rhy", 0),
        
        // 音频格式默认值
        AUDIO_ENCODING("encoding", "lame"),
        SAMPLE_RATE("sample_rate", 16000),
        
        // 拼音标注默认值
        PYBUF_ENCODING("encoding", "utf8"),
        PYBUF_COMPRESS("compress", "raw"),
        PYBUF_FORMAT("format", "plain"),
        
        // 文本格式默认值
        TEXT_ENCODING("encoding", "utf8"),
        TEXT_COMPRESS("compress", "raw"),
        TEXT_FORMAT("format", "plain"),
        
        // 发音人默认值
        VCN("vcn", "x4_guanshan");
        
        private final String paramName;
        private final Object defaultValue;
    }

    /**
     * 服务类型枚举
     * 定义了系统支持的各种外部服务类型，包含对应的日志类型
     * 计费策略根据BILLING_CALC_RULE.md文档规则自动确定
     */
    @Getter
    @AllArgsConstructor
    public enum ServiceType {
        // 语音合成服务
        TTS_PREMIUM_SHORT("精品语音合成服务（短文本）", "TTS_PREMIUM_SHORT"),
        TTS_PREMIUM_LONG("精品语音合成服务（长文本）", "TTS_PREMIUM_LONG"),

        // 语音识别服务
        ASR_FILE("语音识别服务（文件）", "ASR_FILE"),
        ASR_REALTIME("语音识别服务（实时）", "ASR_REALTIME"),

        // 文字识别服务
        OCR_EDUCATION("教育场景文字识别服务", "OCR_EDUCATION"),
        OCR_GENERAL("通用场景文字识别服务", "OCR_GENERAL");

        private final String description;
        private final String code;

    }

    /**
     * 讯飞流式TTS默认配置常量
     */
    public static class XfyunStreamTtsDefaults {
        /** 默认WebSocket地址 */
        public static final String DEFAULT_HOST_URL = "wss://tts-api.xfyun.cn/v2/tts";

        /** 默认发音人 */
        public static final String DEFAULT_VCN = "xiaoyan";

        /** 默认音频编码格式 */
        public static final String DEFAULT_AUE = "raw";

        /** 默认音频采样率 */
        public static final String DEFAULT_AUF = "audio/L16;rate=16000";

        /** 默认语速（0-100） */
        public static final Integer DEFAULT_SPEED = 50;

        /** 默认音量（0-100） */
        public static final Integer DEFAULT_VOLUME = 50;

        /** 默认音高（0-100） */
        public static final Integer DEFAULT_PITCH = 50;

        /** 默认合成音频的背景音 */
        public static final Integer DEFAULT_BGS = 0;

        /** 默认文本编码格式 */
        public static final String DEFAULT_TTE = "UTF8";

        /** 默认英文发音方式 */
        public static final String DEFAULT_REG = "0";

        /** 默认合成音频数字发音方式 */
        public static final String DEFAULT_RDN = "0";

        /** 默认连接超时时间（毫秒） */
        public static final Integer DEFAULT_CONNECT_TIMEOUT = 30000;

        /** 默认读取超时时间（毫秒） */
        public static final Integer DEFAULT_READ_TIMEOUT = 60000;

        /** 默认请求超时时间（毫秒） */
        public static final Long DEFAULT_REQUEST_TIMEOUT = 600000L;
    }

    /**
     * 讯飞TTS音频格式映射枚举
     */
    @Getter
    @AllArgsConstructor
    public enum XfyunTtsAudioFormat {
        LAME("lame", "mp3", "audio/mpeg"),
        RAW("raw", "pcm", "application/octet-stream"),
        OPUS("opus", "opus", "audio/opus"),
        OPUS_WB("opus-wb", "opus", "audio/opus"),
        SPEEX("speex", "speex", "audio/speex"),
        SPEEX_WB("speex-wb", "speex", "audio/speex"),
        SPEEX_ORG_WB("speex-org-wb", "speex", "audio/speex"),
        SPEEX_ORG_NB("speex-org-nb", "speex", "audio/speex"),
        WAV("wav", "wav", "audio/wav");

        private final String aueCode;        // 讯飞TTS的aue参数值
        private final String fileExtension;  // 文件扩展名
        private final String contentType;    // HTTP Content-Type

        // aue编码到格式信息的映射
        private static final Map<String, XfyunTtsAudioFormat> AUE_TO_FORMAT_MAP =
                Arrays.stream(XfyunTtsAudioFormat.values())
                        .collect(Collectors.toMap(XfyunTtsAudioFormat::getAueCode, format -> format));

        // 文件扩展名到Content-Type的映射
        private static final Map<String, String> EXTENSION_TO_CONTENT_TYPE_MAP =
                Arrays.stream(XfyunTtsAudioFormat.values())
                        .collect(Collectors.toMap(XfyunTtsAudioFormat::getFileExtension, XfyunTtsAudioFormat::getContentType, (v1, v2) -> v1));

        /**
         * 根据aue编码获取音频格式信息
         *
         * @param aueCode aue编码
         * @return 音频格式信息
         */
        public static Optional<XfyunTtsAudioFormat> getFormatByAue(String aueCode) {
            return Optional.ofNullable(AUE_TO_FORMAT_MAP.get(aueCode));
        }

        /**
         * 根据文件扩展名获取Content-Type
         *
         * @param fileExtension 文件扩展名
         * @return Content-Type
         */
        public static Optional<String> getContentTypeByExtension(String fileExtension) {
            return Optional.ofNullable(EXTENSION_TO_CONTENT_TYPE_MAP.get(fileExtension));
        }
    }

    /**
     * 讯飞流式IAT默认配置常量
     */
    public static class XfyunStreamIatDefaults {
        /** 默认WebSocket地址（中英文） */
        public static final String DEFAULT_HOST_URL = "wss://iat-api.xfyun.cn/v2/iat";

        /** 默认小语种WebSocket地址 */
        public static final String DEFAULT_NICHE_HOST_URL = "wss://iat-niche-api.xfyun.cn/v2/iat";

        /** 默认语种 */
        public static final String DEFAULT_LANGUAGE = "zh_cn";

        /** 默认应用领域 */
        public static final String DEFAULT_DOMAIN = "iat";

        /** 默认方言 */
        public static final String DEFAULT_ACCENT = "mandarin";

        /** 默认后端点检测静默时间（毫秒） */
        public static final Integer DEFAULT_VAD_EOS = 2000;

        /** 默认标点符号添加（1：开启，0：关闭） */
        public static final Integer DEFAULT_PTT = 1;

        /** 默认标点返回位置控制（1：开启，0：关闭） */
        public static final Integer DEFAULT_PCM = 1;

        /** 默认字体 */
        public static final String DEFAULT_RLANG = "zh-cn";

        /** 默认返回端点帧偏移值（1：开启，0：关闭） */
        public static final Integer DEFAULT_VINFO = 0;

        /** 默认数字格式规则（1：开启，0：关闭） */
        public static final Integer DEFAULT_NUNUM = 1;

        /** 默认音频采样率格式 */
        public static final String DEFAULT_FORMAT = "audio/L16;rate=16000";

        /** 默认音频数据格式 */
        public static final String DEFAULT_ENCODING = "raw";

        /** 默认连接超时时间（毫秒） */
        public static final Integer DEFAULT_CONNECT_TIMEOUT = 30000;

        /** 默认读取超时时间（毫秒） */
        public static final Integer DEFAULT_READ_TIMEOUT = 60000;

        /** 默认请求超时时间（毫秒） */
        public static final Long DEFAULT_REQUEST_TIMEOUT = 600000L;
    }

    /**
     * TextIn文档解析默认参数枚举
     */
    @Getter
    @AllArgsConstructor
    public enum TextInRequestParams {
        PAGE_START("page_start", 1),
        PAGE_COUNT("page_count", 1000),
        PARSE_MODE("parse_mode", "auto"),
        DPI("dpi", 144),
        APPLY_DOCUMENT_TREE("apply_document_tree", 1),
        TABLE_FLAVOR("table_flavor", "md"),
        GET_IMAGE("get_image", "none"),
        IMAGE_OUTPUT_TYPE("image_output_type", "default"),
        APPLY_IMAGE_ANALYSIS("apply_image_analysis", 0),
        PARATEXT_MODE("paratext_mode", "none"),
        FORMULA_LEVEL("formula_level", 0),
        APPLY_MERGE("apply_merge", 0),
        MARKDOWN_DETAILS("markdown_details", 0),
        PAGE_DETAILS("page_details", 0),
        RAW_OCR("raw_ocr", 0),
        CHAR_DETAILS("char_details", 0),
        CATALOG_DETAILS("catalog_details", 0),
        GET_EXCEL("get_excel", 0),
        CROP_IMAGE("crop_image", 0),
        REMOVE_WATERMARK("remove_watermark", 0),
        APPLY_CHART("apply_chart", 0);

        private final String paramName;
        private final Object defaultValue;
    }

    /**
     * 讯飞录音文件转写默认配置常量
     */
    public static class XfyunRaasrDefaults {
        /** 默认上传API地址 */
        public static final String DEFAULT_UPLOAD_URL = "https://raasr.xfyun.cn/v2/api/upload";

        /** 默认获取结果API地址 */
        public static final String DEFAULT_GET_RESULT_URL = "https://raasr.xfyun.cn/v2/api/getResult";

        /** 默认语种类型 */
        public static final String DEFAULT_LANGUAGE = "cn";

        /** 默认多候选开关（0：关闭，1：打开） */
        public static final Short DEFAULT_CANDIDATE = 0;

        /** 默认角色分离开关（0：不开启，1：通用角色分离） */
        public static final Short DEFAULT_ROLE_TYPE = 0;

        /** 默认说话人数（0-10，默认为0进行盲分） */
        public static final Short DEFAULT_ROLE_NUM = 0;

        /** 默认转写音频上传方式 */
        public static final String DEFAULT_AUDIO_MODE = "fileStream";

        /** 默认是否标准pcm/wav（0：非标准，1：标准） */
        public static final Integer DEFAULT_STANDARD_WAV = 0;

        /** 默认语言识别模式选择（1：自动中英文模式） */
        public static final Integer DEFAULT_LANGUAGE_TYPE = 1;

        /** 默认按声道分轨转写模式（1：不分轨模式） */
        public static final Short DEFAULT_TRACK_MODE = 1;

        /** 默认翻译模式（2：按段落进行翻译） */
        public static final Short DEFAULT_TRANS_MODE = 2;

        /** 默认顺滑开关 */
        public static final Boolean DEFAULT_ENG_SMOOTHPROC = true;

        /** 默认口语规整开关 */
        public static final Boolean DEFAULT_ENG_COLLOQPROC = false;

        /** 默认远近场模式（1：远场模式） */
        public static final Integer DEFAULT_ENG_VAD_MDN = 1;

        /** 默认首尾是否带静音信息（1：显示） */
        public static final Integer DEFAULT_ENG_VAD_MARGIN = 1;

        /** 默认针对粤语转写后的字体转换（1：输出繁体） */
        public static final Integer DEFAULT_ENG_RLANG = 1;

        /** 默认查询结果类型 */
        public static final String DEFAULT_RESULT_TYPE = "transfer";

        /** 默认连接超时时间（毫秒） */
        public static final Integer DEFAULT_CONNECT_TIMEOUT = 30000;

        /** 默认读取超时时间（毫秒） */
        public static final Integer DEFAULT_READ_TIMEOUT = 60000;
    }
}