package com.fytec.config;

import com.fytec.constant.ExternalConstants;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 讯飞录音文件转写配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "external.xfyun.raasr")
public class XfyunRaasrProperties {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * API密钥
     */
    private String apiSecret;

    /**
     * 文件上传API端点
     */
    private String uploadUrl = ExternalConstants.XfyunRaasrDefaults.DEFAULT_UPLOAD_URL;

    /**
     * 获取结果API端点
     */
    private String getResultUrl = ExternalConstants.XfyunRaasrDefaults.DEFAULT_GET_RESULT_URL;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = ExternalConstants.XfyunRaasrDefaults.DEFAULT_CONNECT_TIMEOUT;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = ExternalConstants.XfyunRaasrDefaults.DEFAULT_READ_TIMEOUT;

    /**
     * 检查配置是否可用
     *
     * @return 是否可用
     */
    public boolean isAvailable() {
        return appId != null && !appId.trim().isEmpty() &&
               apiKey != null && !apiKey.trim().isEmpty() &&
               apiSecret != null && !apiSecret.trim().isEmpty();
    }
}
