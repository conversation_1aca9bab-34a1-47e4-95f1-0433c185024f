package com.fytec.dto.external;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 讯飞录音文件转写上传请求DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "讯飞录音文件转写上传请求")
public class XfyunRaasrUploadRequestDTO {

    // ========== 必须参数 ==========
    
    @NotBlank(message = "音频文件名称不能为空")
    @Schema(description = "音频文件名称，最好携带音频真实的后缀名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fileName;

    @NotNull(message = "音频文件大小不能为空")
    @Schema(description = "音频文件大小（字节数）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long fileSize;

    @NotNull(message = "音频时长不能为空")
    @Schema(description = "音频真实时长（毫秒）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long duration;

    // ========== 可选参数 ==========

    @Schema(description = "语种类型，默认cn（中文）。支持：cn（中文）、en（英文）、ja（日语）、ko（韩语）等")
    private String language;

    @Schema(description = "回调地址，订单完成时回调该地址通知完成")
    private String callbackUrl;

    @Schema(description = "热词，用以提升专业词汇的识别率，格式：热词1|热词2|热词3")
    private String hotWord;

    @Schema(description = "多候选开关，0：关闭（默认），1：打开")
    private Short candidate;

    @Schema(description = "是否开启角色分离，0：不开启角色分离（默认），1：通用角色分离")
    private Short roleType;

    @Schema(description = "说话人数，取值范围0-10，默认为0进行盲分")
    private Short roleNum;

    @Schema(description = "领域个性化参数，如：court（法律）、edu（教育）、finance（金融）、medical（医疗）等")
    private String pd;

    @Schema(description = "转写音频上传方式，fileStream：文件流（默认），urlLink：音频url外链")
    private String audioMode;

    @Schema(description = "音频url外链地址，当audioMode为urlLink时该值必传")
    private String audioUrl;

    @Schema(description = "是否标准pcm/wav(16k/16bit/单声道)，0：非标准wav（默认），1：标准pcm/wav")
    private Integer standardWav;

    @Schema(description = "语言识别模式选择，1：自动中英文模式（默认），2：中文模式，4：纯中文模式")
    private Integer languageType;

    @Schema(description = "按声道分轨转写模式，1：不分轨模式（默认），2：双声道分轨模式")
    private Short trackMode;

    @Schema(description = "需要翻译的语种（转写语种和翻译语种不能相同）")
    private String transLanguage;

    @Schema(description = "翻译模式，1：按VAD进行翻译，2：按段落进行翻译（默认），3：按整篇进行翻译")
    private Short transMode;

    @Schema(description = "控制分段的最大字数，取值范围[0-500]")
    private Integer engSegMax;

    @Schema(description = "控制分段的最小字数，取值范围[0-50]")
    private Integer engSegMin;

    @Schema(description = "控制分段字数的权重，取值（0-0.05）")
    private Float engSegWeight;

    @Schema(description = "顺滑开关，true：表示开启（默认），false：表示关闭")
    private Boolean engSmoothproc;

    @Schema(description = "口语规整开关，true：表示开启，false：表示关闭（默认）")
    private Boolean engColloqproc;

    @Schema(description = "远近场模式，1：远场模式（默认），2：近场模式")
    private Integer engVadMdn;

    @Schema(description = "首尾是否带静音信息，0：不显示，1：显示（默认）")
    private Integer engVadMargin;

    @Schema(description = "针对粤语转写后的字体转换，0：输出简体，1：输出繁体（默认）")
    private Integer engRlang;

    // ========== 音频流模式专用参数 ==========

    @Schema(description = "音频文件数据（音频流模式使用），当audioMode为fileStream且提供此字段时，将使用音频流模式上传")
    private byte[] audioData;
}
