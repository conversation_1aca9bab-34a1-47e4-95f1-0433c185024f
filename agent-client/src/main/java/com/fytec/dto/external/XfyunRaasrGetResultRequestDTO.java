package com.fytec.dto.external;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 讯飞录音文件转写获取结果请求DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "讯飞录音文件转写获取结果请求")
public class XfyunRaasrGetResultRequestDTO {

    // ========== 必须参数 ==========
    
    @NotBlank(message = "订单ID不能为空")
    @Schema(description = "非实时转写订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderId;

    // ========== 可选参数 ==========

    @Schema(description = "查询结果类型，默认返回转写结果。transfer：转写结果，translate：翻译结果，predict：质检结果")
    private String resultType;
}
