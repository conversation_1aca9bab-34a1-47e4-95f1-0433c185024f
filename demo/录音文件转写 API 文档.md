# 录音文件转写 API 文档

## [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#接口说明)接口说明

录音文件转写（Long Form ASR）基于深度全序列卷积神经网络，将长段音频（**5小时以内**）数据转换成文本数据，为信息处理和数据挖掘提供基础。
转写的是已录制音频（非实时），音频文件上传成功后进入等待队列，待转写成功后用户即可获取结果，返回结果时间受音频时长以及排队任务量的影响。 **如遇转写耗时比平时延长，大概率表示当前时间段出现转写高峰，请耐心等待即可**，我们承诺有效任务耗时最大不超过5小时，详情请参考[SLA协议 ](https://www.xfyun.cn/doc/policy/SLA.html)。
另外，为使转写服务更加通畅，**请尽量转写5分钟以上的音频文件**，上传大量的短音频易引起网络和服务器资源紧张，从而导致任务排队积压。
**音频时长与理论返回时间可以参考下表（请注意，实际返回时长受上传的音频时长和任务总量影响，忙时会出现任务排队情况）：**

| 音频时长X（分钟） | 参考返回时间Y（分钟） |
| ----------------- | --------------------- |
| X<10              | Y<3                   |
| 10<=X<30          | 3<=Y<6                |
| 30<=X<60          | 6<=Y<10               |
| 60<=X             | 10<=Y<20              |

- 接口Demo: 部分开发语言demo如下，其他开发语言请参照文档进行开发，也欢迎热心的开发者到 [讯飞开放平台社区 ](https://developer.xfyun.cn/)分享你们的demo。
  [语音转写demo java语言](https://xfyun-doc.xfyun.cn/static%2F16905382489113458%2Fraasr_demo.zip)
  [语音转写demo python语言](https://xfyun-doc.xfyun.cn/static/16735945125044764/lfasr_new_python_demo.zip)
  [语音转写JAVA-SDK-DEMO](https://github.com/iFLYTEK-OP/websdk-java-demo)

语音转写API JAVA-SDK-DEMO一分钟调用视频如下：

<video width="640" height="360" controls="controls" id="raasr_xlliu24_202504161114" style="color: rgb(44, 62, 80); font-family: -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"></video>



- [有奖调研 ](https://www.wjx.cn/vm/PUSoy5t.aspx)诚邀您参与我们转写服务有奖调研，参与问卷即有机会获取超长服务包

## [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#接口要求)接口要求

| 内容             | 说明                                                         |
| :--------------- | :----------------------------------------------------------- |
| 请求协议         | http[s]（为提高安全性，强烈推荐https）                       |
| 请求地址         | 1、文件上传：http[s]: //raasr.xfyun.cn/v2/api/upload 2、获取结果：http[s]: //raasr.xfyun.cn/v2/api/getResult *注：服务器IP不固定，为保证您的接口稳定，请勿通过指定IP的方式调用接口，使用域名方式调用* |
| 请求方式         | upload[POST]、getResult[GET]                                 |
| 接口鉴权         | 签名机制，详见下方[signa生成](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#signa生成) |
| 字符编码         | UTF-8                                                        |
| 响应格式         | 统一采用JSON格式                                             |
| 开发语言         | 任意，只要可以向讯飞云服务发起HTTP请求的均可                 |
| 音频属性         | 采样率16k或8k、位长8bit或16bit、单声道&多声道                |
| 音频格式         | mp3,wav,pcm,aac,opus,flac,ogg,m4a,amr,speex（微信）,lyb,ac3,aac,ape,m4r,mp4,acc,wma |
| 音频大小         | 不超过500M                                                   |
| 音频时长         | 不超过5小时，建议5分钟以上                                   |
| 语言种类         | 中文普通话、英文，小语种以及中文方言可以到控制台-语音转写-方言/语种处添加试用或购买 |
| 转写结果保存时长 | 已完成订单（包含成功和失败）会在识别完成 72 小时后删除，即无法再被查到结果 |
| 获取结果次数     | 不得超过100次                                                |
| SLA保障时长      | 返回时长最大不超过5小时，赔偿标准等详情请参考[SLA协议](https://www.xfyun.cn/doc/policy/SLA.html) |

## [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#_1、文件上传)1、文件上传

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#概述)概述

```txt
 首先调用文件上传接口，上传待转写音频文件的基本信息（文件名、大小等）和相关的可配置参数。
 调用成功，返回订单ID（orderId，用于查询结果或者联调排查问题时使用），是后续接口的必传参数。
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#请求示例)请求示例

```json
https://raasr.xfyun.cn/v2/api/upload?duration=200&signa=Je5YsBvPcsbB4qy8Qvzd367fiv0%3D&fileName=%E9%98%B3%E5%85%89%E6%80%BB%E5%9C%A8%E9%A3%8E%E9%9B%A8%E5%90%8E.speex-wb&fileSize=11895&sysDicts=uncivilizedLanguage&appId=3e79d91c&ts=1662101767
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#url)URL

```http
 POST https: //raasr.xfyun.cn/v2/api/upload
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#请求头)请求头

```http
        Content-Type: application/json; charset=UTF-8,Chunked: false
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#signa生成)signa生成

① 获取baseString

```text
baseString 由 appid 和当前时间戳 ts 拼接而成；
假如 appid = 595f23df，ts = 1512041814，则 baseString = 595f23df1512041814
```

② 对 baseString 进行 MD5

```text
假如 baseString 为上一步生成的 595f23df1512041814，MD5 之后则为 0829d4012497c14a30e7e72aeebe565e
```

③ 以 secret key 为 key 对 MD5 之后的 baseString 进行 HmacSHA1 加密，然后再对加密后的字符串进行 base64 编码。

```text
假如 secretkey = d9f4aa7ea6d94faca62cd88a28fd5234，
MD5 之后的 baseString 为上一步生成的 0829d4012497c14a30e7e72aeebe565e，
则 HmacSHA1 加密之后再进行 base64 编码得到的 signa 为： IrrzsJeOFk1NGfJHW6SkHUoN9CU=
```

备注：

- secretkey：接口密钥，在应用中添加语音转写服务后，显示在服务管理页面，请调用方注意保管；
- signa 的生成公式：HmacSHA1(MD5(appid + ts)，secretkey)，具体的生成方法详见【调用示例】；

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#请求参数)请求参数

*注：如下参数请参数拼接在 url 中，如果是音频流模式，音频文件需要放在 body 体中，同时设置 http 的 header（Content-Type 为 application/octet-stream）。*

| 参数           | 类型    | 必须 | 说明                                                         |
| :------------- | :------ | :--- | :----------------------------------------------------------- |
| fileName       | String  | 是   | 音频文件名称，最好携带音频真实的后缀名，避免影响转码         |
| fileSize       | Long    | 是   | 音频文件大小（字节数），当前只针对本地文件流方式校验，使用url外链方式不校验，可随机传一个数字。传递真实的音频文件大小，音频流模式服务端会根据这个参数和实际获取到的进行比较，不一致可能是文件丢失直接导致创建订单失败 |
| duration       | Long    | 是   | 音频真实时长.当前未验证，可随机传一个数字                    |
| language       | String  | 否   | 语种类型：默认 cn cn：中文，通用方言（包括普通话、天津、河北、东北、甘肃、山东、太原） en：英文 ja：日语 ko：韩语 ru：俄语 fr：法语 es：西班牙语 vi：越南语 ar：阿拉伯语 cn_xinanese：西南官话（包括四川、重庆、云南、贵州） cn_cantonese：粤语 cn_henanese； 河南 cn_uyghur：维吾尔语 cn_tibetan：藏语 ar:阿拉伯语 de:德语 it:意大利语 重要提示：非实时语音转写结合统一建模的星火多语种语音识别大模型，推出英语en、日语ja、韩语ko、俄语ru、法语fr、西班牙语es、阿拉伯语ar、德语de、葡萄牙语pt、越南语vi、泰语th、意大利语it、印地语hi转写语种识别。极大提升了语音转写准确度，真实还原语音内容，标点等同步预测，带来更流畅的体验。后续将持续扩增转写语种的支持。快来[点击提交工单 ](https://console.xfyun.cn/workorder/commit)进行申请体验吧！ |
| callbackUrl    | String  | 否   | 回调地址 订单完成时回调该地址通知完成支持get 请求，我们会在回调地址中拼接参数，长度限制512： http://{ip}/{port}?xxx&OrderId=xxxx&status=1 参数：orderId为订单号、status为订单状态: 1(转写识别成功) 、-1(转写识别失败) |
| hotWord        | String  | 否   | 热词，用以提升专业词汇的识别率 格式：热词1\| 热词2\| 热词3 单个热词长度： [2,16] ，格式要求正则：[\uD800\uDC00-\uDBFF\uDFFF\uD800-\uDFFF]，热词个数限制 200个 |
| candidate      | Short   | 否   | 多候选开关 0：关闭 (默认) 1：打开                            |
| roleType       | Short   | 否   | 是否开启角色分离 0：不开启角色分离(默认) 1：通用角色分离     |
| roleNum        | Short   | 否   | 说话人数，取值范围 0-10，默认为 0 进行盲分 注：该字段只有在开通了 角色分离功能 的前提下才会生效，正确传入该参数后角色分离效果会有所提升 |
| pd             | String  | 否   | 领域个性化参数 court：法律 edu：教育 finance：金融 medical：医疗 tech： 科技 culture：人文历史 isp：运营商 sport：体育 gov：政府 game：游戏 ecom：电商 mil：军事 com：企业 life：生活 ent：娱乐 car：汽车 |
| audioMode      | String  | 否   | 转写音频上传方式 fileStream：文件流 (默认) urlLink：音频url外链 |
| audioUrl       | String  | 否   | 音频url外链地址 当audioMode为urlLink时该值必传； 如果url中包含特殊字符，audioUrl 需要UrlEncode(不包含签名时需要的 UrlEncode)，长度限制512 |
| standardWav    | Int     | 否   | 是否标准pcm/wav(16k/16bit/单声道) 0：非标准 wav (默认) 1：标准pcm/wav |
| languageType   | Int     | 否   | 语言识别模式选择，支持的语言识别模式选择如下：language 为 cn 时：1：自动中英文模式 (默认) 2:中文模式（可能包含少量英文） 4:纯中文模式（不包含英文） |
| trackMode      | Short   | 否   | 按声道分轨转写模式（支持语种：cn、en）： 1- 不分轨模式 (默认)； 2- 双声道分轨模式（确保音频为双声道，否则会报错）； 备注：如果转写任务使用双声道分轨模式，角色分离（roleType） 功能失效。转写结果字段请参考 getResult 接口协议定义字段 |
| transLanguage  | String  | 否   | 需要翻译的语种(转写语种和翻译语种不能相同) 支持的语种请参考[语种支持](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#语种支持) |
| transMode      | Short   | 否   | 翻译模式（默认 2：按段落进行翻译，目前只支持按段落进行翻译），使用翻译能力时该字段生效 1：按 VAD 进行翻译； 2：按段落进行翻译； 3：按整篇进行翻译； |
| eng_seg_max    | Int     | 否   | 控制分段的最大字数，取值范围[0-500]，不传使用引擎默认值      |
| eng_seg_min    | Int     | 否   | 控制分段的最小字数，取值范围[0-50]，不传使用引擎默认值       |
| eng_seg_weight | Float   | 否   | 控制分段字数的权重，权重比越高，表示引擎分段逻辑采用字数控制分段的比重越高。取值（0-0.05）不传即不采用字数控制分段，采用引擎默认分段逻辑 |
| eng_smoothproc | boolean | 否   | 顺滑开关 true：表示开启 (默认) false：表示关闭               |
| eng_colloqproc | boolean | 否   | 口语规整开关，口语规整是顺滑的升级版本 true：表示开启 false：表示关闭 (默认) 1.当 eng_smoothproc 为 false，eng_colloqproc 为 false 时只返回原始转写结果 2.当 eng_smoothproc 为 true，eng_colloqproc 为 false 时返回包含顺滑词的结果和原始结果 3. 当 eng_smoothproc 为 true，eng_colloqproc 为 true 时返回包含口语规整的结果和原始结果 4. 当 eng_smoothproc 为 false，eng_colloqproc 为 true 时返回包含口语规整的结果和原始结果 |
| eng_vad_mdn    | int     | 否   | 远近场模式 1：远场模式 (默认) 2：近场模式                    |
| eng_vad_margin | int     | 否   | 首尾是否带静音信息 0：不显示 1：显示 (默认)                  |
| eng_rlang      | int     | 否   | 针对粤语转写后的字体转换 0：输出简体 1：输出繁体 (默认)      |

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#返回示例)返回示例

成功

```json
{
 "code": "000000",
 "descInfo": "success",
 "content": {
  "orderId": "DKHJQ202209021522090215490FAAE7DD0008C",
  "taskEstimateTime": 28000
 }
}
```

失败

```json
{
 "code": "26600",
 "descInfo": "转写业务通用错误"
}
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#返回参数)返回参数

| 参数    | 类型   | 说明                                              |
| ------- | ------ | ------------------------------------------------- |
| orderId | String | 调用成功，orderId即为订单ID，是后续接口的必传参数 |

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#转写结束异步回调状态)转写结束异步回调状态

当订单转写流程结束时会回调用户（如果录音文件转写接口 upload 传了callbackUrl），会把订单号和订单状态返回，具体的格式和参数说明如下： 回调地址示例：

```text
GET http://ip:prot/server/xxx?orderId=DKHJQ202004291620042916580FBC96690001F&status=1
```

| 参数名     | 类型   | 必传 | 描述                                                         |
| ---------- | ------ | ---- | ------------------------------------------------------------ |
| orderId    | String | 是   | 转写订单号，用于查询使用                                     |
| status     | String | 是   | 订单状态： -1：失败 1：成功 注： 1、成功需要调用 getResult 接口查询转写结果数据； 2、如果任务包含翻译环节且任务在转码、转写环节失败不会进行翻译的回调； |
| resultType | String | 否   | 回调任务类型：为空或不包含该字段时为转写回调 转写：transfer； 翻译：translate； 质检：predict； |

## [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#_2、查询结果)2、查询结果

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#概述-2)概述

```txt
根据实际应用场景该接口调用分两种场景：
正常场景：转写订单按时完成会返回订单号和对应的状态，如果成功，集成方在接收到我们回调成功的 请求后调用该接口查询转写结果。
异常场景：即我们回调集成方超时或失败后（我们有重试几次的机制）或者服务订单有积压，按照转写耗时的评估表在订单创建成功后超过预计的转写耗时后可主动调用该接口查询结果，可以考虑采用分梯度间 隔时间查询（避免一直轮询导致其他正常订单结果的查询，因为我们对接口查询频率有限制）。
```

**注：已完成订单（包含成功和失败）会在识别完成 72 小时后删除，即无法再被查到结果。**

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#请求示例-2)请求示例

```text
https://raasr.xfyun.cn/v2/api/getResult?signa=Wv23VLOg%2F6sQ1BDx4DKnnxtgiwQ%3D&orderId=DKHJQ2022090217220902175209AAEBD000015&appId=3e79d91c&resultType=predict&ts=1662112340
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#url-2)url

```http
 POST http[s]://raasr.xfyun.cn/v2/api/getResult
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#请求头-2)请求头

```http
 Content-Type: multipart/form-data;
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#请求参数-2)请求参数

| 参数       | 类型   | 必须 | 描述                                                         |
| :--------- | :----- | :--- | :----------------------------------------------------------- |
| orderId    | String | 是   | 非实时转写订单号                                             |
| resultType | String | 否   | 查询结果类型：默认返回转写结果 转写结果：transfer； 翻译结果：translate； 质检结果：predict； 组合结果查询：多个类型结果使用”,”隔开，目前只支持转写和质检结果一起返回，不支持转写和翻译结果一起返回（如果任务有失败则只返回处理成功的结果） 转写和质检结果组合返回：transfer，predict **注：使用质检功能请先在控制台开启** |

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#返回示例-2)返回示例

```json
{
 "code": "000000",
 "descInfo": "success",
 "content": {
  "orderInfo": {
   "orderId": "DKHJQ2022090510220905100562536FEF00062",
   "failType": 0,
   "status": 4,
   "originalDuration": 200,
   "realDuration": 1878
  },
  "orderResult": "{\"lattice\":[{\"json_1best\":\"{\\\"st\\\":{\\\"sc\\\":\\\"0.86\\\",\\\"pa\\\":\\\"0\\\",\\\"rt\\\":[{\\\"ws\\\":[{\\\"cw\\\":[{\\\"w\\\":\\\"这\\\",\\\"wp\\\":\\\"n\\\",\\\"wc\\\":\\\"1.0000\\\"}],\\\"wb\\\":1,\\\"we\\\":16},{\\\"cw\\\":[{\\\"w\\\":\\\"是\\\",\\\"wp\\\":\\\"n\\\",\\\"wc\\\":\\\"1.0000\\\"}],\\\"wb\\\":17,\\\"we\\\":36},{\\\"cw\\\":[{\\\"w\\\":\\\"一\\\",\\\"wp\\\":\\\"n\\\",\\\"wc\\\":\\\"1.0000\\\"}],\\\"wb\\\":37,\\\"we\\\":52},{\\\"cw\\\":[{\\\"w\\\":\\\"条\\\",\\\"wp\\\":\\\"n\\\",\\\"wc\\\":\\\"1.0000\\\"}],\\\"wb\\\":53,\\\"we\\\":80},{\\\"cw\\\":[{\\\"w\\\":\\\"测试\\\",\\\"wp\\\":\\\"n\\\",\\\"wc\\\":\\\"1.0000\\\"}],\\\"wb\\\":81,\\\"we\\\":116},{\\\"cw\\\":[{\\\"w\\\":\\\"音频\\\",\\\"wp\\\":\\\"n\\\",\\\"wc\\\":\\\"1.0000\\\"}],\\\"wb\\\":117,\\\"we\\\":172},{\\\"cw\\\":[{\\\"w\\\":\\\"。\\\",\\\"wp\\\":\\\"p\\\",\\\"wc\\\":\\\"0.0000\\\"}],\\\"wb\\\":172,\\\"we\\\":172},{\\\"cw\\\":[{\\\"w\\\":\\\"\\\",\\\"wp\\\":\\\"g\\\",\\\"wc\\\":\\\"0.0000\\\"}],\\\"wb\\\":172,\\\"we\\\":172}]}],\\\"bg\\\":\\\"50\\\",\\\"rl\\\":\\\"0\\\",\\\"ed\\\":\\\"1840\\\"}}\"}],\"lattice2\":[{\"lid\":\"0\",\"end\":\"1840\",\"begin\":\"50\",\"json_1best\":{\"st\":{\"sc\":\"0.86\",\"pa\":\"0\",\"rt\":[{\"nb\":\"1\",\"nc\":\"1.0\",\"ws\":[{\"cw\":[{\"w\":\"这\",\"wp\":\"n\",\"wc\":\"1.0000\"}],\"wb\":1,\"we\":16},{\"cw\":[{\"w\":\"是\",\"wp\":\"n\",\"wc\":\"1.0000\"}],\"wb\":17,\"we\":36},{\"cw\":[{\"w\":\"一\",\"wp\":\"n\",\"wc\":\"1.0000\"}],\"wb\":37,\"we\":52},{\"cw\":[{\"w\":\"条\",\"wp\":\"n\",\"wc\":\"1.0000\"}],\"wb\":53,\"we\":80},{\"cw\":[{\"w\":\"测试\",\"wp\":\"n\",\"wc\":\"1.0000\"}],\"wb\":81,\"we\":116},{\"cw\":[{\"w\":\"音频\",\"wp\":\"n\",\"wc\":\"1.0000\"}],\"wb\":117,\"we\":172},{\"cw\":[{\"w\":\"。\",\"wp\":\"p\",\"wc\":\"0.0000\"}],\"wb\":172,\"we\":172},{\"cw\":[{\"w\":\"\",\"wp\":\"g\",\"wc\":\"0.0000\"}],\"wb\":172,\"we\":172}]}],\"pt\":\"reserved\",\"bg\":\"50\",\"si\":\"0\",\"rl\":\"0\",\"ed\":\"1840\"}},\"spk\":\"段落-0\"}]}",
  "taskEstimateTime": 0
 }
}
```

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#返回参数-2)返回参数

| 参数名                     | 类型         | 描述                                                         |
| -------------------------- | ------------ | ------------------------------------------------------------ |
| orderResult                | String       | 转写结果                                                     |
| orderInfo                  | Object       | 转写订单信息                                                 |
| taskEstimateTime           | Int          | 订单预估耗时，单位毫秒                                       |
| transResult                | List<String> | 翻译结果，请参考 TransResult                                 |
| predictResult              | String       | 质检结果，请参考 PredictResult                               |
| orderInfo.failType         | Int          | 订单异常状态 0：音频正常执行 1：音频上传失败 2：音频转码失败 3：音频识别失败 4：音频时长超限（最大音频时长为 5 小时） 5：音频校验失败（duration 对应的值与真实音频时长不符合要求） 6：静音文件 7：翻译失败 8：账号无翻译权限 9：转写质检失败 10：转写质检未匹配出关键词 11：upload接口创建任务时，未开启质检或者翻译能力； 备注： resultType=translate，未开启翻译能力； resultType=predict，未开启质检能力； 99：其他 |
| orderInfo.status           | Int          | 订单流程状态 0：订单已创建 3：订单处理中 4：订单已完成 -1：订单失败 |
| orderInfo.orderId          | String       | 订单 Id                                                      |
| orderInfo.originalDuration | Long         | 原始音频时长，单位毫秒                                       |
| orderInfo.realDuration     | Long         | 真实音频时长，单位毫秒                                       |

**orderResult 字段：**

| 参数名   | 类型   | 描述                                                         |
| -------- | ------ | ------------------------------------------------------------ |
| lattice  | List   | 做顺滑功能的识别结果                                         |
| lattice2 | List   | 未做顺滑功能的识别结果，当开启顺滑和后语规整后 orderResult 才返回 lattice2 字段（需要开通权限） |
| label    | Object | 转写结果标签信息，用于补充转写结果相关信息，目前开启双通道转写时该对象会返回，标记转写结果角色和声道的对应关系 |

Lattice 字段：

| 参数名     | 类型   | 描述                        |
| ---------- | ------ | --------------------------- |
| json_1best | String | 单个 vad 的结果的 json 内容 |

json_1best 字段：

| 参数名 | 类型   | 描述               |
| ------ | ------ | ------------------ |
| st     | Object | 单个句子的结果对象 |

st 字段：

| 参数名 | 类型   | 描述                                                         |
| ------ | ------ | ------------------------------------------------------------ |
| bg     | String | 单个句子的开始时间，单位毫秒                                 |
| ed     | String | 单个句子的结束时间，单位毫秒                                 |
| rl     | String | 分离的角色编号，取值正整数，需开启角色分离的功能才返回对应的分离角色编号 |
| rt     | List   | 输出词语识别结果集合                                         |

ws 字段（词语候选识别结果）：

| 参数名 | 类型 | 描述                                                         |
| ------ | ---- | ------------------------------------------------------------ |
| wb     | Long | 词语开始的帧数（注一帧 10ms），位置是相对 bg，仅支持中、英文语种 |
| we     | Long | 词语结束的帧数（注一帧 10ms），位置是相对 bg，仅支持中、英文语种 |
| cw     | List | 词语候选识别结果集合                                         |

cw 字段：

| 参数名 | 类型   | 描述                                                         |
| ------ | ------ | ------------------------------------------------------------ |
| w      | String | 识别结果                                                     |
| wp     | String | 词语的属性 n：正常词 s：顺滑 p：标点 g：分段（按此标识进行分段） |

label 字段：

| 参数名   | 类型 | 描述                                                         |
| -------- | ---- | ------------------------------------------------------------ |
| rl_track | List | 双通道模式转写结果中角色和音频轨道对应信息，开启分轨模式该字段会返回 |

rl_track 字段：

| 参数名 | 类型   | 描述                              |
| ------ | ------ | --------------------------------- |
| rl     | String | 分离的角色编号，取值正整数        |
| track  | String | 音频轨道信息 L：左声道，R：右声道 |

**TransResult 字段：**

| 参数名 | 类型         | 描述     |
| ------ | ------------ | -------- |
| segId  | String       | 段落序号 |
| dst    | String       | 翻译结果 |
| bg     | int          | 开始时间 |
| ed     | int          | 结束时间 |
| tags   | List<String> | 标签     |
| roles  | List<String> | 角色     |

**PredictResult 字段：**

| 参数名                | 类型            | 必传 | 描述                                 |
| --------------------- | --------------- | ---- | ------------------------------------ |
| keywords              | List<KeyWord>   | 是   | 关键词相关信息，请参考：KeyWord 对象 |
| keywords.word         | String          | 是   | 质检关键词内容                       |
| keywords.label        | String          | 是   | 词库标签信息                         |
| keywords.timeStamp    | List<TimeStamp> | 是   | 质检关键词出现位置时间戳信息         |
| keywords.timeStamp.bg | Long            | 是   | 词出现的开启位置时间戳               |
| keywords.timeStamp.ed | Long            | 是   | 词出现的结束位置时间戳               |

## [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#语种支持)语种支持

| 语种名称 | 语种编码     |
| -------- | ------------ |
| 中文     | cn           |
| 英文     | en           |
| 日语     | ja           |
| 韩语     | ko           |
| 俄语     | ru           |
| 法语     | fr           |
| 西班牙语 | es           |
| 越南语   | vi           |
| 粤语     | cn_cantonese |
| 维吾尔语 | cn_uyghur    |
| 藏语     | cn_tibetan   |
| 阿拉伯语 | ar           |
| 德语     | de           |
| 意大利语 | it           |

## [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#错误码)错误码

备注：如出现下述列表中没有的错误码，可到[这里 ](https://www.xfyun.cn/document/error-code)查询。

| 错误码 | 描述                                                         |
| ------ | ------------------------------------------------------------ |
| 26600  | 转写业务通用错误，检查下请求参数是否正确                     |
| 26601  | 非法应用信息，检查下上传的appi是否正确                       |
| 26602  | 任务ID不存在                                                 |
| 26603  | 接口访问频率受限                                             |
| 26604  | 获取结果次数超过限制                                         |
| 26605  | 任务正在处理中，请稍后重试                                   |
| 26606  | 空音频，请检查                                               |
| 26607  | 转写语种未授权或已过有效期                                   |
| 26610  | 请求参数错误                                                 |
| 26621  | 预处理文件大小受限（500M）                                   |
| 26622  | 预处理音频时长受限（5小时）                                  |
| 26623  | 预处理音频格式受限                                           |
| 26625  | 预处理服务时长不足。您剩余的可用服务时长不足，请移步产品页http://www.xfyun.cn/services/lfasr 进行购买或者免费领取 |
| 26631  | 音频文件大小受限（500M）                                     |
| 26632  | 音频时长受限（5小时）                                        |
| 26633  | 音频服务时长不足。您剩余的可用服务时长不足，请移步产品页http://www.xfyun.cn/services/lfasr 进行购买或者免费领取 |
| 26634  | 文件下载失败                                                 |
| 26635  | 文件长度校验失败                                             |
| 26640  | 文件上传失败                                                 |
| 26641  | 上传分片超过限制                                             |
| 26642  | 分片合并失败                                                 |
| 26643  | 计算音频时长失败,请检查您的音频是否加密或者损坏              |
| 26650  | 音频格式转换失败,请检查您的音频是否加密或者损坏              |
| 26660  | 计费计量失败                                                 |
| 26670  | 转写结果集解析失败                                           |
| 26671  | 下载转写结果失败                                             |
| 26680  | 引擎错误                                                     |
| 26681  | 引擎获取订单异常                                             |
| 26682  | 引擎订单处理中                                               |
| 26689  | 引擎网络异常                                                 |

## [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#常见问题)常见问题

[有奖调研 ](https://www.wjx.cn/vm/PUSoy5t.aspx)诚邀您参与我们转写服务有奖调研，参与问卷即有机会获取超长服务包

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#录音文件转写支持哪些音频格式)录音文件转写支持哪些音频格式？

> 答：目前录音文件转写支持的音频格式为：已录制音频（5小时内），wav,flac,opus,m4a,mp3，单声道&多声道，支持语种：中文普通话、英语、开通的小语种以及中文方言，采样率：8KHz,16KHz

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#录音文件转写支不支持并发)录音文件转写支不支持并发？

> 答：支持，要保证同一个appid每秒请求接口次数最大值在20次以下。

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#录音文件转写可以试用吗)录音文件转写可以试用吗？

> 答：可以领取新用户礼包，根据您认证的程度，提供最多50小时的免费时长，有效期为一年。

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#录音文件转写支持什么语言)录音文件转写支持什么语言？

> 答：支持语种：中文普通话、英语，小语种以及中文方言可以到控制台-语音转写-方言/语种处添加试用或购买；设置方式参考上述语言参数切换即可

#### [#](https://www.xfyun.cn/doc/asr/ifasr_new/API.html#录音文件转写的套餐扣费顺序是怎样的)录音文件转写的套餐扣费顺序是怎样的？

> 答：扣量优先级：免费试用＞批量购买，即在“批量购买”的套餐额度剩余的情况下，又领取了免费试用的体验包，则领取的免费试用体验包立即生效，并被设定为当前扣量套餐。而之前购买的套餐包的额度和到期日不变。